#!/usr/bin/env node

/**
 * Pre-deployment script to check if the project is ready for deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Pre-deployment checks starting...\n');

// Check if required files exist
const requiredFiles = [
  'package.json',
  'vercel.json',
  'vue.config.js',
  'src/main.js',
  'public/index.html'
];

console.log('📁 Checking required files...');
let missingFiles = [];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.log(`\n❌ Missing required files: ${missingFiles.join(', ')}`);
  process.exit(1);
}

// Check package.json scripts
console.log('\n📦 Checking package.json scripts...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const requiredScripts = ['build', 'serve'];
const missingScripts = requiredScripts.filter(script => !packageJson.scripts[script]);

if (missingScripts.length > 0) {
  console.log(`❌ Missing scripts: ${missingScripts.join(', ')}`);
  process.exit(1);
} else {
  console.log('✅ All required scripts present');
}

// Check if build works
console.log('\n🔨 Testing build process...');
try {
  execSync('npm run build', { stdio: 'pipe' });
  console.log('✅ Build successful');
} catch (error) {
  console.log('❌ Build failed');
  console.log(error.stdout?.toString());
  console.log(error.stderr?.toString());
  process.exit(1);
}

// Check if dist folder was created
if (fs.existsSync('dist')) {
  console.log('✅ dist folder created');
  
  // Check if index.html exists in dist
  if (fs.existsSync('dist/index.html')) {
    console.log('✅ dist/index.html exists');
  } else {
    console.log('❌ dist/index.html missing');
    process.exit(1);
  }
} else {
  console.log('❌ dist folder not created');
  process.exit(1);
}

// Check environment variables
console.log('\n🔧 Checking environment configuration...');
if (fs.existsSync('.env.example')) {
  console.log('✅ .env.example exists');
} else {
  console.log('⚠️  .env.example not found (recommended)');
}

// Check vercel.json configuration
console.log('\n⚡ Checking Vercel configuration...');
try {
  const vercelConfig = JSON.parse(fs.readFileSync('vercel.json', 'utf8'));
  
  if (vercelConfig.builds && vercelConfig.builds.length > 0) {
    console.log('✅ Vercel builds configured');
  } else {
    console.log('⚠️  No builds configuration in vercel.json');
  }
  
  if (vercelConfig.routes && vercelConfig.routes.length > 0) {
    console.log('✅ Vercel routes configured');
  } else {
    console.log('⚠️  No routes configuration in vercel.json');
  }
} catch (error) {
  console.log('❌ Invalid vercel.json format');
  process.exit(1);
}

// Final summary
console.log('\n🎉 Pre-deployment checks completed successfully!');
console.log('\n📋 Next steps:');
console.log('1. Commit your changes to Git');
console.log('2. Push to your repository');
console.log('3. Deploy using: vercel --prod');
console.log('4. Or connect your repository to Vercel Dashboard');

console.log('\n💡 Tips:');
console.log('- Test your deployment URL thoroughly');
console.log('- Set up environment variables in Vercel Dashboard');
console.log('- Configure custom domain if needed');
console.log('- Enable Vercel Analytics for monitoring');
