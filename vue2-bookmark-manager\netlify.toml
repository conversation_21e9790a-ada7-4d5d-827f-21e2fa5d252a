# Netlify configuration file (alternative to Vercel)
# This file provides configuration for deploying to Netlify

[build]
  # Build command
  command = "npm run build"
  
  # Directory to publish
  publish = "dist"
  
  # Node.js version
  environment = { NODE_VERSION = "18" }

# Redirect rules for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Build plugins (optional)
# [[plugins]]
#   package = "@netlify/plugin-lighthouse"
