# Vue2 Bookmark Manager - Vercel Deployment Guide

This guide explains how to deploy the Vue2 Bookmark Manager to Vercel.

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Git Repository**: Your code should be in a Git repository (GitHub, GitLab, or Bitbucket)

## Deployment Methods

### Method 1: Vercel CLI (Recommended)

1. **Install Vercel CLI**:
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**:
   ```bash
   vercel login
   ```

3. **Deploy from project directory**:
   ```bash
   cd vue2-bookmark-manager
   vercel
   ```

4. **Follow the prompts**:
   - Set up and deploy? `Y`
   - Which scope? Choose your account
   - Link to existing project? `N` (for first deployment)
   - What's your project's name? `vue2-bookmark-manager`
   - In which directory is your code located? `./`

### Method 2: Vercel Dashboard

1. **Connect Repository**:
   - Go to [vercel.com/dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your Git repository

2. **Configure Build Settings**:
   - Framework Preset: `Vue.js`
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Install Command: `npm install`

3. **Deploy**:
   - Click "Deploy"
   - Wait for the build to complete

## Configuration Files

The following files have been added/configured for Vercel deployment:

### `vercel.json`
- Configures build settings and routing
- Sets up SPA routing (all routes redirect to index.html)
- Adds security headers
- Configures static asset caching

### `package.json`
- Added `vercel-build` script
- Optimized for production builds

### `vue.config.js`
- Production optimizations
- Asset chunking for better caching
- Disabled source maps for smaller builds

### `.vercelignore`
- Excludes unnecessary files from deployment
- Reduces deployment size and time

## Environment Variables

If your app uses environment variables:

1. **Local Development**:
   Create `.env.local` file:
   ```
   VUE_APP_API_URL=http://localhost:3000/api
   ```

2. **Vercel Dashboard**:
   - Go to Project Settings → Environment Variables
   - Add your variables for Production, Preview, and Development

3. **Vercel CLI**:
   ```bash
   vercel env add VUE_APP_API_URL
   ```

## Custom Domain

1. **Add Domain**:
   - Go to Project Settings → Domains
   - Add your custom domain
   - Follow DNS configuration instructions

2. **SSL Certificate**:
   - Automatically provided by Vercel
   - No additional configuration needed

## Performance Optimizations

The deployment includes several optimizations:

- **Code Splitting**: Vendor and common chunks separated
- **Asset Optimization**: Images and static files optimized
- **Caching Headers**: Long-term caching for static assets
- **Compression**: Gzip compression enabled by default

## Monitoring and Analytics

1. **Vercel Analytics**:
   - Enable in Project Settings → Analytics
   - Track page views and performance

2. **Function Logs**:
   - View in Vercel Dashboard → Functions tab
   - Monitor API calls and errors

## Troubleshooting

### Build Failures

1. **Check build logs** in Vercel Dashboard
2. **Common issues**:
   - Missing dependencies: Check `package.json`
   - Build command errors: Verify `npm run build` works locally
   - Memory issues: Contact Vercel support for limits

### Routing Issues

1. **SPA Routing**: Ensure `vercel.json` has correct routing rules
2. **Base URL**: Check `publicPath` in `vue.config.js`

### Performance Issues

1. **Bundle Size**: Use `npm run build -- --report` to analyze
2. **Loading Speed**: Check Vercel Analytics for insights

## Continuous Deployment

Once connected to Git:

1. **Automatic Deployments**:
   - Push to main branch → Production deployment
   - Push to other branches → Preview deployments

2. **Preview URLs**:
   - Each commit gets a unique preview URL
   - Perfect for testing before merging

## Support

- **Vercel Documentation**: [vercel.com/docs](https://vercel.com/docs)
- **Vue CLI Documentation**: [cli.vuejs.org](https://cli.vuejs.org/)
- **Project Issues**: Create an issue in the repository

## Next Steps

After successful deployment:

1. **Test all functionality** on the live site
2. **Set up monitoring** and analytics
3. **Configure custom domain** if needed
4. **Set up environment variables** for different environments
5. **Enable preview deployments** for development workflow
