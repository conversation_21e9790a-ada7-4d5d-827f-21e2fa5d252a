!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).zrender={})}(this,function(t){"use strict";var e=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},c=new function(){this.browser=new e,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(c.wxa=!0,c.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?c.worker=!0:!c.hasGlobalWindow||"Deno"in window?(c.node=!0,c.svgSupported=!0):function(t,e){var r=e.browser,i=t.match(/Firefox\/([\d.]+)/),n=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(r.firefox=!0,r.version=i[1]);n&&(r.ie=!0,r.version=n[1]);o&&(r.edge=!0,r.version=o[1],r.newEdge=18<+o[1].split(".")[0]);a&&(r.weChat=!0);e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!r.ie&&!r.edge,e.pointerEventsSupported="onpointerdown"in window&&(r.edge||r.ie&&11<=+r.version),e.domSupported="undefined"!=typeof document;var s=document.documentElement.style;e.transform3dSupported=(r.ie&&"transition"in s||r.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||r.ie&&9<=+r.version}(navigator.userAgent,c);var h=12,v="sans-serif",E=h+"px "+v;var l,u,p=function(t){var e={};if("undefined"==typeof JSON)return e;for(var r=0;r<t.length;r++){var i=String.fromCharCode(r+32),n=(t.charCodeAt(r)-20)/100;e[i]=n}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),f={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){var r;if(l||(r=f.createCanvas(),l=r&&r.getContext("2d")),l)return u!==e&&(u=l.font=e||E),l.measureText(t);t=t||"";var i=/((?:\d+)?\.?\d*)px/.exec(e=e||E),n=i&&+i[1]||h,o=0;if(0<=e.indexOf("mono"))o=n*t.length;else for(var a=0;a<t.length;a++){var s=p[t[a]];o+=null==s?n:s*n}return{width:o}},loadImage:function(t,e,r){var i=new Image;return i.onload=e,i.onerror=r,i.src=t,i}};var s=D(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),d=D(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),y=Object.prototype.toString,r=Array.prototype,a=r.forEach,g=r.filter,n=r.slice,_=r.map,i=function(){}.constructor,o=i?i.prototype:null,m="__proto__",x=2311;function w(){return x++}function b(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function C(t){if(null==t||"object"!=typeof t)return t;var e=t,r=y.call(t);if("[object Array]"===r){if(!rt(t)){e=[];for(var i=0,n=t.length;i<n;i++)e[i]=C(t[i])}}else if(d[r]){if(!rt(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,n=t.length;i<n;i++)e[i]=t[i]}}}else if(!s[r]&&!rt(t)&&!Y(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==m&&(e[a]=C(t[a]));return e}function S(t,e,r){if(!W(e)||!W(t))return r?C(e):t;for(var i in e){var n,o;e.hasOwnProperty(i)&&i!==m&&(n=t[i],!W(o=e[i])||!W(n)||G(o)||G(n)||Y(o)||Y(n)||X(o)||X(n)||rt(o)||rt(n)?!r&&i in t||(t[i]=C(e[i])):S(n,o,r))}return t}function I(t,e){if(Object.assign)Object.assign(t,e);else for(var r in e)e.hasOwnProperty(r)&&r!==m&&(t[r]=e[r]);return t}function k(t,e,r){for(var i=F(e),n=0,o=i.length;n<o;n++){var a=i[n];(r?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}var T=f.createCanvas;function P(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var r=0,i=t.length;r<i;r++)if(t[r]===e)return r}return-1}function M(t,e,r){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),n=0;n<i.length;n++){var o=i[n];"constructor"!==o&&(r?null!=e[o]:null==t[o])&&(t[o]=e[o])}else k(t,e,r)}function A(t){return!!t&&("string"!=typeof t&&"number"==typeof t.length)}function O(t,e,r){if(t&&e)if(t.forEach&&t.forEach===a)t.forEach(e,r);else if(t.length===+t.length)for(var i=0,n=t.length;i<n;i++)e.call(r,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(r,t[o],o,t)}function L(t,e,r){if(!t)return[];if(!e)return Q(t);if(t.map&&t.map===_)return t.map(e,r);for(var i=[],n=0,o=t.length;n<o;n++)i.push(e.call(r,t[n],n,t));return i}function D(t,e,r,i){if(t&&e){for(var n=0,o=t.length;n<o;n++)r=e.call(i,r,t[n],n,t);return r}}function R(t,e,r){if(!t)return[];if(!e)return Q(t);if(t.filter&&t.filter===g)return t.filter(e,r);for(var i=[],n=0,o=t.length;n<o;n++)e.call(r,t[n],n,t)&&i.push(t[n]);return i}function F(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var r in t)t.hasOwnProperty(r)&&e.push(r);return e}var z=o&&B(o.bind)?o.call.bind(o.bind):function(t,e){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return function(){return t.apply(e,r.concat(n.call(arguments)))}};function G(t){return Array.isArray?Array.isArray(t):"[object Array]"===y.call(t)}function B(t){return"function"==typeof t}function N(t){return"string"==typeof t}function H(t){return"number"==typeof t}function W(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function X(t){return!!s[y.call(t)]}function q(t){return!!d[y.call(t)]}function Y(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function j(t){return null!=t.colorStops}function V(t){return null!=t.image}function U(t){return t!=t}function Z(t,e){return null!=t?t:e}function K(t,e,r){return null!=t?t:null!=e?e:r}function Q(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return n.apply(t,e)}function $(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function J(t,e){if(!t)throw new Error(e)}function tt(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var et="__ec_primitive__";function rt(t){return t[et]}var it=(nt.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},nt.prototype.has=function(t){return this.data.hasOwnProperty(t)},nt.prototype.get=function(t){return this.data[t]},nt.prototype.set=function(t,e){return this.data[t]=e,this},nt.prototype.keys=function(){return F(this.data)},nt.prototype.forEach=function(t){var e=this.data;for(var r in e)e.hasOwnProperty(r)&&t(e[r],r)},nt);function nt(){this.data={}}var ot="function"==typeof Map;var at=(st.prototype.hasKey=function(t){return this.data.has(t)},st.prototype.get=function(t){return this.data.get(t)},st.prototype.set=function(t,e){return this.data.set(t,e),e},st.prototype.each=function(r,i){this.data.forEach(function(t,e){r.call(i,t,e)})},st.prototype.keys=function(){var t=this.data.keys();return ot?Array.from(t):t},st.prototype.removeKey=function(t){this.data.delete(t)},st);function st(t){var r=G(t);this.data=new(ot?Map:it);var i=this;function e(t,e){r?i.set(t,e):i.set(e,t)}t instanceof st?t.each(e):t&&O(t,e)}function ht(t,e){var r,i=Object.create?Object.create(t):((r=function(){}).prototype=t,new r);return e&&I(i,e),i}function lt(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function ut(t,e){return t.hasOwnProperty(e)}function ct(){}var pt=180/Math.PI,ft=Object.freeze({__proto__:null,guid:w,logError:b,clone:C,merge:S,mergeAll:function(t,e){for(var r=t[0],i=1,n=t.length;i<n;i++)r=S(r,t[i],e);return r},extend:I,defaults:k,createCanvas:T,indexOf:P,inherits:function(t,e){var r=t.prototype;function i(){}for(var n in i.prototype=e.prototype,t.prototype=new i,r)r.hasOwnProperty(n)&&(t.prototype[n]=r[n]);(t.prototype.constructor=t).superClass=e},mixin:M,isArrayLike:A,each:O,map:L,reduce:D,filter:R,find:function(t,e,r){if(t&&e)for(var i=0,n=t.length;i<n;i++)if(e.call(r,t[i],i,t))return t[i]},keys:F,bind:z,curry:function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return function(){return t.apply(this,e.concat(n.call(arguments)))}},isArray:G,isFunction:B,isString:N,isStringSafe:function(t){return"[object String]"===y.call(t)},isNumber:H,isObject:W,isBuiltInObject:X,isTypedArray:q,isDom:Y,isGradientObject:j,isImagePatternObject:V,isRegExp:function(t){return"[object RegExp]"===y.call(t)},eqNaN:U,retrieve:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0,i=t.length;r<i;r++)if(null!=t[r])return t[r]},retrieve2:Z,retrieve3:K,slice:Q,normalizeCssArray:$,assert:J,trim:tt,setAsPrimitive:function(t){t[et]=!0},isPrimitive:rt,HashMap:at,createHashMap:function(t){return new at(t)},concatArray:function(t,e){for(var r=new t.constructor(t.length+e.length),i=0;i<t.length;i++)r[i]=t[i];for(var n=t.length,i=0;i<e.length;i++)r[i+n]=e[i];return r},createObject:ht,disableUserSelect:lt,hasOwn:ut,noop:ct,RADIAN_TO_DEGREE:pt}),dt=function(t,e){return(dt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};function yt(t,e){function r(){this.constructor=t}dt(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function vt(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function gt(t){return[t[0],t[1]]}function _t(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t}function mt(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t}function xt(t){return Math.sqrt(wt(t))}function wt(t){return t[0]*t[0]+t[1]*t[1]}function bt(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t}function St(t,e){var r=xt(e);return 0===r?(t[0]=0,t[1]=0):(t[0]=e[0]/r,t[1]=e[1]/r),t}function kt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Tt=kt;function Ct(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Pt=Ct;function Mt(t,e,r,i){return t[0]=e[0]+i*(r[0]-e[0]),t[1]=e[1]+i*(r[1]-e[1]),t}function At(t,e,r){var i=e[0],n=e[1];return t[0]=r[0]*i+r[2]*n+r[4],t[1]=r[1]*i+r[3]*n+r[5],t}function Lt(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t}function Dt(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t}var zt=Object.freeze({__proto__:null,create:vt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:gt,set:function(t,e,r){return t[0]=e,t[1]=r,t},add:_t,scaleAndAdd:function(t,e,r,i){return t[0]=e[0]+r[0]*i,t[1]=e[1]+r[1]*i,t},sub:mt,len:xt,length:xt,lenSquare:wt,lengthSquare:wt,mul:function(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t},div:function(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:bt,normalize:St,distance:kt,dist:Tt,distanceSquare:Ct,distSquare:Pt,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:Mt,applyTransform:At,min:Lt,max:Dt}),It=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},Ot=(Rt.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new It(e,t),"dragstart",t.event))},Rt.prototype._drag=function(t){var e,r,i,n,o,a,s=this._draggingTarget;s&&(e=t.offsetX,r=t.offsetY,i=e-this._x,n=r-this._y,this._x=e,this._y=r,s.drift(i,n,t),this.handler.dispatchToElement(new It(s,t),"drag",t.event),o=this.handler.findHover(e,r,s).target,a=this._dropTarget,s!==(this._dropTarget=o)&&(a&&o!==a&&this.handler.dispatchToElement(new It(a,t),"dragleave",t.event),o&&o!==a&&this.handler.dispatchToElement(new It(o,t),"dragenter",t.event)))},Rt.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new It(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new It(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},Rt);function Rt(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}var Ft=(Bt.prototype.on=function(t,e,r,i){this._$handlers||(this._$handlers={});var n=this._$handlers;if("function"==typeof e&&(i=r,r=e,e=null),!r||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),n[t]||(n[t]=[]);for(var a=0;a<n[t].length;a++)if(n[t][a].h===r)return this;var s={h:r,query:e,ctx:i||this,callAtLast:r.zrEventfulCallAtLast},h=n[t].length-1,l=n[t][h];return l&&l.callAtLast?n[t].splice(h,0,s):n[t].push(s),this},Bt.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},Bt.prototype.off=function(t,e){var r=this._$handlers;if(!r)return this;if(!t)return this._$handlers={},this;if(e){if(r[t]){for(var i=[],n=0,o=r[t].length;n<o;n++)r[t][n].h!==e&&i.push(r[t][n]);r[t]=i}r[t]&&0===r[t].length&&delete r[t]}else delete r[t];return this},Bt.prototype.trigger=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var h=i[s];if(!n||!n.filter||null==h.query||n.filter(t,h.query))switch(o){case 0:h.h.call(h.ctx);break;case 1:h.h.call(h.ctx,e[0]);break;case 2:h.h.call(h.ctx,e[0],e[1]);break;default:h.h.apply(h.ctx,e)}}return n&&n.afterTrigger&&n.afterTrigger(t),this},Bt.prototype.triggerWithContext=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,h=0;h<s;h++){var l=i[h];if(!n||!n.filter||null==l.query||n.filter(t,l.query))switch(o){case 0:l.h.call(a);break;case 1:l.h.call(a,e[0]);break;case 2:l.h.call(a,e[0],e[1]);break;default:l.h.apply(a,e.slice(1,o-1))}}return n&&n.afterTrigger&&n.afterTrigger(t),this},Bt);function Bt(t){t&&(this._$eventProcessor=t)}var Nt=Math.log(2);function Ht(t,e,r,i,n,o){var a=i+"-"+n,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var h=Math.round(Math.log((1<<s)-1&~n)/Nt);return t[r][h]}for(var l=i|1<<r,u=r+1;i&1<<u;)u++;for(var c=0,p=0,f=0;p<s;p++){var d=1<<p;d&n||(c+=(f%2?-1:1)*t[r][p]*Ht(t,e-1,u,l,n|d,o),f++)}return o[a]=c}function Et(t,e){var r=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},n=Ht(r,8,0,0,0,i);if(0!==n){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*Ht(r,7,0===a?1:0,1<<a,1<<s,i)/n*e[a];return function(t,e,r){var i=e*o[6]+r*o[7]+1;t[0]=(e*o[0]+r*o[1]+o[2])/i,t[1]=(e*o[3]+r*o[4]+o[5])/i}}}var Wt="___zrEVENTSAVED";function Xt(t,e,r,i,n){if(e.getBoundingClientRect&&c.domSupported&&!qt(e)){var o=e[Wt]||(e[Wt]={}),a=function(t,e,r){for(var i=r?"invTrans":"trans",n=e[i],o=e.srcCoords,a=[],s=[],h=!0,l=0;l<4;l++){var u=t[l].getBoundingClientRect(),c=2*l,p=u.left,f=u.top;a.push(p,f),h=h&&o&&p===o[c]&&f===o[1+c],s.push(t[l].offsetLeft,t[l].offsetTop)}return h&&n?n:(e.srcCoords=a,e[i]=r?Et(s,a):Et(a,s))}(function(t,e){var r=e.markers;if(r)return r;r=e.markers=[];for(var i=["left","right"],n=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,h=o%2,l=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[h]+":0",n[l]+":0",i[1-h]+":auto",n[1-l]+":auto",""].join("!important;"),t.appendChild(a),r.push(a)}return r}(e,o),o,n);if(a)return a(t,r,i),1}}function qt(t){return"CANVAS"===t.nodeName.toUpperCase()}var Yt=/([&<>"'])/g,jt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};var Vt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ut=[],Gt=c.browser.firefox&&+c.browser.version.split(".")[0]<39;function Zt(t,e,r,i){return r=r||{},i?Kt(t,e,r):Gt&&null!=e.layerX&&e.layerX!==e.offsetX?(r.zrX=e.layerX,r.zrY=e.layerY):null!=e.offsetX?(r.zrX=e.offsetX,r.zrY=e.offsetY):Kt(t,e,r),r}function Kt(t,e,r){if(c.domSupported&&t.getBoundingClientRect){var i=e.clientX,n=e.clientY;if(qt(t)){var o=t.getBoundingClientRect();return r.zrX=i-o.left,void(r.zrY=n-o.top)}if(Xt(Ut,t,i,n))return r.zrX=Ut[0],void(r.zrY=Ut[1])}r.zrX=r.zrY=0}function Qt(t){return t||window.event}function $t(t,e,r){if(null!=(e=Qt(e)).zrX)return e;var i,n,o=e.type;o&&0<=o.indexOf("touch")?(i="touchend"!==o?e.targetTouches[0]:e.changedTouches[0])&&Zt(t,i,e,r):(Zt(t,e,e,r),n=function(t){var e=t.wheelDelta;if(e)return e;var r=t.deltaX,i=t.deltaY;return null!=r&&null!=i?3*(0!==i?Math.abs(i):Math.abs(r))*(0<i||!(i<0)&&0<r?-1:1):e}(e),e.zrDelta=n?n/120:-(e.detail||0)/3);var a=e.button;return null==e.which&&void 0!==a&&Vt.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}var Jt=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},te=(ee.prototype.recognize=function(t,e,r){return this._doTrack(t,e,r),this._recognize(t)},ee.prototype.clear=function(){return this._track.length=0,this},ee.prototype._doTrack=function(t,e,r){var i=t.touches;if(i){for(var n={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],h=Zt(r,s,{});n.points.push([h.zrX,h.zrY]),n.touches.push(s)}this._track.push(n)}},ee.prototype._recognize=function(t){for(var e in ie)if(ie.hasOwnProperty(e)){var r=ie[e](this._track,t);if(r)return r}},ee);function ee(){this._track=[]}function re(t){var e=t[1][0]-t[0][0],r=t[1][1]-t[0][1];return Math.sqrt(e*e+r*r)}var ie={pinch:function(t,e){var r=t.length;if(r){var i,n=(t[r-1]||{}).points,o=(t[r-2]||{}).points||n;if(o&&1<o.length&&n&&1<n.length){var a=re(n)/re(o);isFinite(a)||(a=1),e.pinchScale=a;var s=[((i=n)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}};function ne(){return[1,0,0,1,0,0]}function oe(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ae(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function se(t,e,r){var i=e[0]*r[0]+e[2]*r[1],n=e[1]*r[0]+e[3]*r[1],o=e[0]*r[2]+e[2]*r[3],a=e[1]*r[2]+e[3]*r[3],s=e[0]*r[4]+e[2]*r[5]+e[4],h=e[1]*r[4]+e[3]*r[5]+e[5];return t[0]=i,t[1]=n,t[2]=o,t[3]=a,t[4]=s,t[5]=h,t}function he(t,e,r){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+r[0],t[5]=e[5]+r[1],t}function le(t,e,r,i){void 0===i&&(i=[0,0]);var n=e[0],o=e[2],a=e[4],s=e[1],h=e[3],l=e[5],u=Math.sin(r),c=Math.cos(r);return t[0]=n*c+s*u,t[1]=-n*u+s*c,t[2]=o*c+h*u,t[3]=-o*u+c*h,t[4]=c*(a-i[0])+u*(l-i[1])+i[0],t[5]=c*(l-i[1])-u*(a-i[0])+i[1],t}function ue(t,e,r){var i=r[0],n=r[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function ce(t,e){var r=e[0],i=e[2],n=e[4],o=e[1],a=e[3],s=e[5],h=r*a-o*i;return h?(h=1/h,t[0]=a*h,t[1]=-o*h,t[2]=-i*h,t[3]=r*h,t[4]=(i*s-a*n)*h,t[5]=(o*n-r*s)*h,t):null}var pe=Object.freeze({__proto__:null,create:ne,identity:oe,copy:ae,mul:se,translate:he,rotate:le,scale:ue,invert:ce,clone:function(t){var e=ne();return ae(e,t),e}}),fe=(de.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},de.prototype.clone=function(){return new de(this.x,this.y)},de.prototype.set=function(t,e){return this.x=t,this.y=e,this},de.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},de.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},de.prototype.scale=function(t){this.x*=t,this.y*=t},de.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},de.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},de.prototype.dot=function(t){return this.x*t.x+this.y*t.y},de.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},de.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},de.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},de.prototype.distance=function(t){var e=this.x-t.x,r=this.y-t.y;return Math.sqrt(e*e+r*r)},de.prototype.distanceSquare=function(t){var e=this.x-t.x,r=this.y-t.y;return e*e+r*r},de.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},de.prototype.transform=function(t){if(t){var e=this.x,r=this.y;return this.x=t[0]*e+t[2]*r+t[4],this.y=t[1]*e+t[3]*r+t[5],this}},de.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},de.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},de.set=function(t,e,r){t.x=e,t.y=r},de.copy=function(t,e){t.x=e.x,t.y=e.y},de.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},de.lenSquare=function(t){return t.x*t.x+t.y*t.y},de.dot=function(t,e){return t.x*e.x+t.y*e.y},de.add=function(t,e,r){t.x=e.x+r.x,t.y=e.y+r.y},de.sub=function(t,e,r){t.x=e.x-r.x,t.y=e.y-r.y},de.scale=function(t,e,r){t.x=e.x*r,t.y=e.y*r},de.scaleAndAdd=function(t,e,r,i){t.x=e.x+r.x*i,t.y=e.y+r.y*i},de.lerp=function(t,e,r,i){var n=1-i;t.x=n*e.x+i*r.x,t.y=n*e.y+i*r.y},de);function de(t,e){this.x=t||0,this.y=e||0}var ye=Math.min,ve=Math.max,ge=new fe,_e=new fe,me=new fe,xe=new fe,we=new fe,be=new fe,Se=(ke.prototype.union=function(t){var e=ye(t.x,this.x),r=ye(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=ve(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=ve(t.y+t.height,this.y+this.height)-r:this.height=t.height,this.x=e,this.y=r},ke.prototype.applyTransform=function(t){ke.applyTransform(this,this,t)},ke.prototype.calculateTransform=function(t){var e=t.width/this.width,r=t.height/this.height,i=ne();return he(i,i,[-this.x,-this.y]),ue(i,i,[e,r]),he(i,i,[t.x,t.y]),i},ke.prototype.intersect=function(t,e){if(!t)return!1;t instanceof ke||(t=ke.create(t));var r,i,n,o,a,s,h,l,u=this,c=u.x,p=u.x+u.width,f=u.y,d=u.y+u.height,y=t.x,v=t.x+t.width,g=t.y,_=t.y+t.height,m=!(p<y||v<c||d<g||_<f);return e&&(r=1/0,i=0,n=Math.abs(p-y),o=Math.abs(v-c),a=Math.abs(d-g),s=Math.abs(_-f),h=Math.min(n,o),l=Math.min(a,s),p<y||v<c?i<h&&(i=h,n<o?fe.set(be,-n,0):fe.set(be,o,0)):h<r&&(r=h,n<o?fe.set(we,n,0):fe.set(we,-o,0)),d<g||_<f?i<l&&(i=l,a<s?fe.set(be,0,-a):fe.set(be,0,s)):h<r&&(r=h,a<s?fe.set(we,0,a):fe.set(we,0,-s))),e&&fe.copy(e,m?we:be),m},ke.prototype.contain=function(t,e){var r=this;return t>=r.x&&t<=r.x+r.width&&e>=r.y&&e<=r.y+r.height},ke.prototype.clone=function(){return new ke(this.x,this.y,this.width,this.height)},ke.prototype.copy=function(t){ke.copy(this,t)},ke.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},ke.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},ke.prototype.isZero=function(){return 0===this.width||0===this.height},ke.create=function(t){return new ke(t.x,t.y,t.width,t.height)},ke.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},ke.applyTransform=function(t,e,r){if(r){if(r[1]<1e-5&&-1e-5<r[1]&&r[2]<1e-5&&-1e-5<r[2]){var i=r[0],n=r[3],o=r[4],a=r[5];return t.x=e.x*i+o,t.y=e.y*n+a,t.width=e.width*i,t.height=e.height*n,t.width<0&&(t.x+=t.width,t.width=-t.width),void(t.height<0&&(t.y+=t.height,t.height=-t.height))}ge.x=me.x=e.x,ge.y=xe.y=e.y,_e.x=xe.x=e.x+e.width,_e.y=me.y=e.y+e.height,ge.transform(r),xe.transform(r),_e.transform(r),me.transform(r),t.x=ye(ge.x,_e.x,me.x,xe.x),t.y=ye(ge.y,_e.y,me.y,xe.y);var s=ve(ge.x,_e.x,me.x,xe.x),h=ve(ge.y,_e.y,me.y,xe.y);t.width=s-t.x,t.height=h-t.y}else t!==e&&ke.copy(t,e)},ke);function ke(t,e,r,i){r<0&&(t+=r,r=-r),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=r,this.height=i}var Te="silent";function Ce(){Jt(this.event)}var Pe,Me=(yt(Ae,Pe=Ft),Ae.prototype.dispose=function(){},Ae.prototype.setCursor=function(){},Ae);function Ae(){var t=null!==Pe&&Pe.apply(this,arguments)||this;return t.handler=null,t}var Le,De=function(t,e){this.x=t,this.y=e},ze=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Ie=new Se(0,0,0,0),Oe=(yt(Re,Le=Ft),Re.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(O(ze,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},Re.prototype.mousemove=function(t){var e=t.zrX,r=t.zrY,i=Be(this,e,r),n=this._hovered,o=n.target;o&&!o.__zr&&(o=(n=this.findHover(n.x,n.y)).target);var a=this._hovered=i?new De(e,r):this.findHover(e,r),s=a.target,h=this.proxy;h.setCursor&&h.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},Re.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},Re.prototype.resize=function(){this._hovered=new De(0,0)},Re.prototype.dispatch=function(t,e){var r=this[t];r&&r.call(this,e)},Re.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},Re.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},Re.prototype.dispatchToElement=function(t,e,r){var i=(t=t||{}).target;if(!i||!i.silent){for(var n,o,a="on"+e,s={type:e,event:o=r,target:(n=t).target,topTarget:n.topTarget,cancelBubble:!1,offsetX:o.zrX,offsetY:o.zrY,gestureEvent:o.gestureEvent,pinchX:o.pinchX,pinchY:o.pinchY,pinchScale:o.pinchScale,wheelDelta:o.zrDelta,zrByTouch:o.zrByTouch,which:o.which,stop:Ce};i&&(i[a]&&(s.cancelBubble=!!i[a].call(i,s)),i.trigger(e,s),i=i.__hostTarget?i.__hostTarget:i.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(e,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[a]&&t[a].call(t,s),t.trigger&&t.trigger(e,s)}))}},Re.prototype.findHover=function(t,e,r){var i=this.storage.getDisplayList(),n=new De(t,e);if(Fe(i,n,t,e,r),this._pointerSize&&!n.target){for(var o=[],a=this._pointerSize,s=a/2,h=new Se(t-s,e-s,a,a),l=i.length-1;0<=l;l--){var u=i[l];u===r||u.ignore||u.ignoreCoarsePointer||u.parent&&u.parent.ignoreCoarsePointer||(Ie.copy(u.getBoundingRect()),u.transform&&Ie.applyTransform(u.transform),Ie.intersect(h)&&o.push(u))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,f=0;f<s;f+=4)for(var d=0;d<p;d+=c)if(Fe(o,n,t+f*Math.cos(d),e+f*Math.sin(d),r),n.target)return n}return n},Re.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new te);var r=this._gestureMgr;"start"===e&&r.clear();var i,n,o=r.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);"end"===e&&r.clear(),o&&(i=o.type,t.gestureEvent=i,(n=new De).target=o.target,this.dispatchToElement(n,i,o.event))},Re);function Re(t,e,r,i,n){var o=Le.call(this)||this;return o._hovered=new De(0,0),o.storage=t,o.painter=e,o.painterRoot=i,o._pointerSize=n,r=r||new Me,o.proxy=null,o.setHandlerProxy(r),o._draggingMgr=new Ot(o),o}function Fe(t,e,r,i,n){for(var o=t.length-1;0<=o;o--){var a=t[o],s=void 0;if(a!==n&&!a.ignore&&(s=function(t,e,r){if(t[t.rectHover?"rectContain":"contain"](e,r)){for(var i=t,n=void 0,o=!1;i;){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,r))return!1}i.silent&&(n=!0);var s=i.__hostTarget,i=s||i.parent}return!n||Te}return!1}(a,r,i))&&(e.topTarget||(e.topTarget=a),s!==Te)){e.target=a;break}}}function Be(t,e,r){var i=t.painter;return e<0||e>i.getWidth()||r<0||r>i.getHeight()}O(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){Oe.prototype[a]=function(t){var e,r,i=t.zrX,n=t.zrY,o=Be(this,i,n);if("mouseup"===a&&o||(r=(e=this.findHover(i,n)).target),"mousedown"===a)this._downEl=r,this._downPoint=[t.zrX,t.zrY],this._upEl=r;else if("mouseup"===a)this._upEl=r;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<Tt(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}});var Ne=32,He=7;function Ee(t,e,r,i){var n=e+1;if(n===r)return 1;if(i(t[n++],t[e])<0){for(;n<r&&i(t[n],t[n-1])<0;)n++;!function(t,e,r){r--;for(;e<r;){var i=t[e];t[e++]=t[r],t[r--]=i}}(t,e,n)}else for(;n<r&&0<=i(t[n],t[n-1]);)n++;return n-e}function We(t,e,r,i,n){for(i===e&&i++;i<r;i++){for(var o,a=t[i],s=e,h=i;s<h;)n(a,t[o=s+h>>>1])<0?h=o:s=1+o;var l=i-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<l;)t[s+l]=t[s+l-1],l--}t[s]=a}}function Xe(t,e,r,i,n,o){var a=0,s=0,h=1;if(0<o(t,e[r+n])){for(s=i-n;h<s&&0<o(t,e[r+n+h]);)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s),a+=n,h+=n}else{for(s=n+1;h<s&&o(t,e[r+n-h])<=0;)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s);var l=a,a=n-h,h=n-l}for(a++;a<h;){var u=a+(h-a>>>1);0<o(t,e[r+u])?a=u+1:h=u}return h}function qe(t,e,r,i,n,o){var a=0,s=0,h=1;if(o(t,e[r+n])<0){for(s=n+1;h<s&&o(t,e[r+n-h])<0;)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s);var l=a,a=n-h,h=n-l}else{for(s=i-n;h<s&&0<=o(t,e[r+n+h]);)(h=1+((a=h)<<1))<=0&&(h=s);s<h&&(h=s),a+=n,h+=n}for(a++;a<h;){var u=a+(h-a>>>1);o(t,e[r+u])<0?h=u:a=u+1}return h}function Ye(d,y){var a,s,v=He,h=0,g=[];function e(t){var e=a[t],r=s[t],i=a[t+1],n=s[t+1];s[t]=r+n,t===h-3&&(a[t+1]=a[t+2],s[t+1]=s[t+2]),h--;var o=qe(d[i],d,e,r,0,y);e+=o,0!=(r-=o)&&0!==(n=Xe(d[e+r-1],d,i,n,n-1,y))&&(r<=n?function(t,e,r,i){var n=0;for(n=0;n<e;n++)g[n]=d[t+n];var o=0,a=r,s=t;if(d[s++]=d[a++],0==--i){for(n=0;n<e;n++)d[s+n]=g[o+n];return}if(1===e){for(n=0;n<i;n++)d[s+n]=d[a+n];return d[s+i]=g[o]}var h,l,u,c=v;for(;;){l=h=0,u=!1;do{if(y(d[a],g[o])<0){if(d[s++]=d[a++],l++,(h=0)==--i){u=!0;break}}else if(d[s++]=g[o++],h++,l=0,1==--e){u=!0;break}}while((h|l)<c);if(u)break;do{if(0!==(h=qe(d[a],g,o,e,0,y))){for(n=0;n<h;n++)d[s+n]=g[o+n];if(s+=h,o+=h,(e-=h)<=1){u=!0;break}}if(d[s++]=d[a++],0==--i){u=!0;break}if(0!==(l=Xe(g[o],d,a,i,0,y))){for(n=0;n<l;n++)d[s+n]=d[a+n];if(s+=l,a+=l,0===(i-=l)){u=!0;break}}if(d[s++]=g[o++],1==--e){u=!0;break}c--}while(He<=h||He<=l);if(u)break;c<0&&(c=0),c+=2}if((v=c)<1&&(v=1),1===e){for(n=0;n<i;n++)d[s+n]=d[a+n];d[s+i]=g[o]}else{if(0===e)throw new Error;for(n=0;n<e;n++)d[s+n]=g[o+n]}}:function(t,e,r,i){var n=0;for(n=0;n<i;n++)g[n]=d[r+n];var o=t+e-1,a=i-1,s=r+i-1,h=0,l=0;if(d[s--]=d[o--],0==--e){for(h=s-(i-1),n=0;n<i;n++)d[h+n]=g[n];return}if(1===i){for(l=(s-=e)+1,h=(o-=e)+1,n=e-1;0<=n;n--)d[l+n]=d[h+n];return d[s]=g[a]}var u=v;for(;;){var c=0,p=0,f=!1;do{if(y(g[a],d[o])<0){if(d[s--]=d[o--],c++,(p=0)==--e){f=!0;break}}else if(d[s--]=g[a--],p++,c=0,1==--i){f=!0;break}}while((c|p)<u);if(f)break;do{if(0!==(c=e-qe(g[a],d,t,e,e-1,y))){for(e-=c,l=(s-=c)+1,h=(o-=c)+1,n=c-1;0<=n;n--)d[l+n]=d[h+n];if(0===e){f=!0;break}}if(d[s--]=g[a--],1==--i){f=!0;break}if(0!==(p=i-Xe(d[o],g,0,i,i-1,y))){for(i-=p,l=(s-=p)+1,h=(a-=p)+1,n=0;n<p;n++)d[l+n]=g[h+n];if(i<=1){f=!0;break}}if(d[s--]=d[o--],0==--e){f=!0;break}u--}while(He<=c||He<=p);if(f)break;u<0&&(u=0),u+=2}(v=u)<1&&(v=1);if(1===i){for(l=(s-=e)+1,h=(o-=e)+1,n=e-1;0<=n;n--)d[l+n]=d[h+n];d[s]=g[a]}else{if(0===i)throw new Error;for(h=s-(i-1),n=0;n<i;n++)d[h+n]=g[n]}})(e,r,i,n)}return a=[],s=[],{mergeRuns:function(){for(;1<h;){var t=h-2;if(1<=t&&s[t-1]<=s[t]+s[t+1]||2<=t&&s[t-2]<=s[t]+s[t-1])s[t-1]<s[t+1]&&t--;else if(s[t]>s[t+1])break;e(t)}},forceMergeRuns:function(){for(;1<h;){var t=h-2;0<t&&s[t-1]<s[t+1]&&t--,e(t)}},pushRun:function(t,e){a[h]=t,s[h]=e,h+=1}}}function je(t,e,r,i){r=r||0;var n=(i=i||t.length)-r;if(!(n<2)){var o=0;if(n<Ne)We(t,r,i,r+(o=Ee(t,r,i,e)),e);else{var a,s=Ye(t,e),h=function(t){for(var e=0;Ne<=t;)e|=1&t,t>>=1;return t+e}(n);do{(o=Ee(t,r,i,e))<h&&(h<(a=n)&&(a=h),We(t,r,r+a,r+o,e),o=a),s.pushRun(r,o),s.mergeRuns(),n-=o,r+=o}while(0!==n);s.forceMergeRuns()}}}var Ve=1,Ue=4,Ge=!1;function Ze(){Ge||(Ge=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Ke(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var Qe=($e.prototype.traverse=function(t,e){for(var r=0;r<this._roots.length;r++)this._roots[r].traverse(t,e)},$e.prototype.getDisplayList=function(t,e){e=e||!1;var r=this._displayList;return!t&&r.length||this.updateDisplayList(e),r},$e.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,r=this._displayList,i=0,n=e.length;i<n;i++)this._updateAndAddDisplayable(e[i],null,t);r.length=this._displayListLen,je(r,Ke)},$e.prototype._updateAndAddDisplayable=function(t,e,r){if(!t.ignore||r){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var n=i,o=t;n;)n.parent=o,n.updateTransform(),e.push(n),n=(o=n).getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var h=a[s];t.__dirty&&(h.__dirty|=Ve),this._updateAndAddDisplayable(h,e,r)}t.__dirty=0}else{var l=t;e&&e.length?l.__clipPaths=e:l.__clipPaths&&0<l.__clipPaths.length&&(l.__clipPaths=[]),isNaN(l.z)&&(Ze(),l.z=0),isNaN(l.z2)&&(Ze(),l.z2=0),isNaN(l.zlevel)&&(Ze(),l.zlevel=0),this._displayList[this._displayListLen++]=l}var u=t.getDecalElement&&t.getDecalElement();u&&this._updateAndAddDisplayable(u,e,r);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,r);var p=t.getTextContent();p&&this._updateAndAddDisplayable(p,e,r)}},$e.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},$e.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,r=t.length;e<r;e++)this.delRoot(t[e]);else{var i=P(this._roots,t);0<=i&&this._roots.splice(i,1)}},$e.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},$e.prototype.getRoots=function(){return this._roots},$e.prototype.dispose=function(){this._displayList=null,this._roots=null},$e);function $e(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Ke}var Je=c.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},tr={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,r=.1;return 0===t?0:1===t?1:(e=!r||r<1?(r=1,.1):.4*Math.asin(1/r)/(2*Math.PI),-(r*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(e=!r||r<1?(r=1,.1):.4*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(e=!r||r<1?(r=1,.1):.4*Math.asin(1/r)/(2*Math.PI),(t*=2)<1?r*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:r*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-tr.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*tr.bounceIn(2*t):.5*tr.bounceOut(2*t-1)+.5}},er=Math.pow,rr=Math.sqrt,ir=1e-8,nr=1e-4,or=rr(3),ar=1/3,sr=vt(),hr=vt(),lr=vt();function ur(t){return-ir<t&&t<ir}function cr(t){return ir<t||t<-ir}function pr(t,e,r,i,n){var o=1-n;return o*o*(o*t+3*n*e)+n*n*(n*i+3*o*r)}function fr(t,e,r,i,n){var o=1-n;return 3*(((e-t)*o+2*(r-e)*n)*o+(i-r)*n*n)}function dr(t,e,r,i,n,o){var a,s,h,l,u,c,p,f,d,y,v,g,_=i+3*(e-r)-t,m=3*(r-2*e+t),x=3*(e-t),w=t-n,b=m*m-3*_*x,S=m*x-9*_*w,k=x*x-3*m*w,T=0;return ur(b)&&ur(S)?ur(m)?o[0]=0:0<=(y=-x/m)&&y<=1&&(o[T++]=y):ur(a=S*S-4*b*k)?(v=-(s=S/b)/2,0<=(y=-m/_+s)&&y<=1&&(o[T++]=y),0<=v&&v<=1&&(o[T++]=v)):0<a?(u=b*m+1.5*_*(-S-(h=rr(a))),0<=(y=(-m-((l=(l=b*m+1.5*_*(-S+h))<0?-er(-l,ar):er(l,ar))+(u=u<0?-er(-u,ar):er(u,ar))))/(3*_))&&y<=1&&(o[T++]=y)):(c=(2*b*m-3*_*S)/(2*rr(b*b*b)),p=Math.acos(c)/3,y=(-m-2*(f=rr(b))*(d=Math.cos(p)))/(3*_),v=(-m+f*(d+or*Math.sin(p)))/(3*_),g=(-m+f*(d-or*Math.sin(p)))/(3*_),0<=y&&y<=1&&(o[T++]=y),0<=v&&v<=1&&(o[T++]=v),0<=g&&g<=1&&(o[T++]=g)),T}function yr(t,e,r,i,n){var o,a,s,h,l=6*r-12*e+6*t,u=9*e+3*i-3*t-9*r,c=3*e-3*t,p=0;return ur(u)?cr(l)&&0<=(s=-c/l)&&s<=1&&(n[p++]=s):ur(o=l*l-4*u*c)?n[0]=-l/(2*u):0<o&&(h=(-l-(a=rr(o)))/(2*u),0<=(s=(-l+a)/(2*u))&&s<=1&&(n[p++]=s),0<=h&&h<=1&&(n[p++]=h)),p}function vr(t,e,r,i,n,o){var a=(e-t)*n+t,s=(r-e)*n+e,h=(i-r)*n+r,l=(s-a)*n+a,u=(h-s)*n+s,c=(u-l)*n+l;o[0]=t,o[1]=a,o[2]=l,o[3]=c,o[4]=c,o[5]=u,o[6]=h,o[7]=i}function gr(t,e,r,i){var n=1-i;return n*(n*t+2*i*e)+i*i*r}function _r(t,e,r,i){return 2*((1-i)*(e-t)+i*(r-e))}function mr(t,e,r){var i=t+r-2*e;return 0==i?.5:(t-e)/i}function xr(t,e,r,i,n){var o=(e-t)*i+t,a=(r-e)*i+e,s=(a-o)*i+o;n[0]=t,n[1]=o,n[2]=s,n[3]=s,n[4]=a,n[5]=r}var wr=/cubic-bezier\(([0-9,\.e ]+)\)/;function br(t){var e=t&&wr.exec(t);if(e){var r=e[1].split(","),i=+tt(r[0]),n=+tt(r[1]),o=+tt(r[2]),a=+tt(r[3]);if(isNaN(i+n+o+a))return;var s=[];return function(t){return t<=0?0:1<=t?1:dr(0,i,o,1,t,s)&&pr(0,n,a,1,s[0])}}}var Sr=(kr.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var r=this._life,i=t-this._startTime-this._pausedTime,n=i/r;n<0&&(n=0),n=Math.min(n,1);var o=this.easingFunc,a=o?o(n):n;if(this.onframe(a),1===n){if(!this.loop)return!0;var s=i%r;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},kr.prototype.pause=function(){this._paused=!0},kr.prototype.resume=function(){this._paused=!1},kr.prototype.setEasing=function(t){this.easing=t,this.easingFunc=B(t)?t:tr[t]||br(t)},kr);function kr(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||ct,this.ondestroy=t.ondestroy||ct,this.onrestart=t.onrestart||ct,t.easing&&this.setEasing(t.easing)}var Tr=function(t){this.value=t},Cr=(Pr.prototype.insert=function(t){var e=new Tr(t);return this.insertEntry(e),e},Pr.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},Pr.prototype.remove=function(t){var e=t.prev,r=t.next;e?e.next=r:this.head=r,r?r.prev=e:this.tail=e,t.next=t.prev=null,this._len--},Pr.prototype.len=function(){return this._len},Pr.prototype.clear=function(){this.head=this.tail=null,this._len=0},Pr);function Pr(){this._len=0}var Mr=(Ar.prototype.put=function(t,e){var r,i,n,o=this._list,a=this._map,s=null;return null==a[t]&&(r=o.len(),i=this._lastRemovedEntry,r>=this._maxSize&&0<r&&(n=o.head,o.remove(n),delete a[n.key],s=n.value,this._lastRemovedEntry=n),i?i.value=e:i=new Tr(e),i.key=t,o.insertEntry(i),a[t]=i),s},Ar.prototype.get=function(t){var e=this._map[t],r=this._list;if(null!=e)return e!==r.tail&&(r.remove(e),r.insertEntry(e)),e.value},Ar.prototype.clear=function(){this._list.clear(),this._map={}},Ar.prototype.len=function(){return this._list.len()},Ar);function Ar(t){this._list=new Cr,this._maxSize=10,this._map={},this._maxSize=t}var Lr={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Dr(t){return(t=Math.round(t))<0?0:255<t?255:t}function zr(t){return t<0?0:1<t?1:t}function Ir(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Dr(parseFloat(e)/100*255):Dr(parseInt(e,10))}function Or(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?zr(parseFloat(e)/100):zr(parseFloat(e))}function Rr(t,e,r){return r<0?r+=1:1<r&&--r,6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function Fr(t,e,r){return t+(e-t)*r}function Br(t,e,r,i,n){return t[0]=e,t[1]=r,t[2]=i,t[3]=n,t}function Nr(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Hr=new Mr(20),Er=null;function Wr(t,e){Er&&Nr(Er,e),Er=Hr.put(t,Er||e.slice())}function Xr(t,e){if(t){e=e||[];var r=Hr.get(t);if(r)return Nr(e,r);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in Lr)return Nr(e,Lr[i]),Wr(t,e),e;var n,o=i.length;if("#"===i.charAt(0))return 4===o||5===o?0<=(n=parseInt(i.slice(1,4),16))&&n<=4095?(Br(e,(3840&n)>>4|(3840&n)>>8,240&n|(240&n)>>4,15&n|(15&n)<<4,5===o?parseInt(i.slice(4),16)/15:1),Wr(t,e),e):void Br(e,0,0,0,1):7===o||9===o?0<=(n=parseInt(i.slice(1,7),16))&&n<=16777215?(Br(e,(16711680&n)>>16,(65280&n)>>8,255&n,9===o?parseInt(i.slice(7),16)/255:1),Wr(t,e),e):void Br(e,0,0,0,1):void 0;var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var h=i.substr(0,a),l=i.substr(a+1,s-(a+1)).split(","),u=1;switch(h){case"rgba":if(4!==l.length)return 3===l.length?Br(e,+l[0],+l[1],+l[2],1):Br(e,0,0,0,1);u=Or(l.pop());case"rgb":return 3<=l.length?(Br(e,Ir(l[0]),Ir(l[1]),Ir(l[2]),3===l.length?u:Or(l[3])),Wr(t,e),e):void Br(e,0,0,0,1);case"hsla":return 4!==l.length?void Br(e,0,0,0,1):(l[3]=Or(l[3]),qr(l,e),Wr(t,e),e);case"hsl":return 3!==l.length?void Br(e,0,0,0,1):(qr(l,e),Wr(t,e),e);default:return}}Br(e,0,0,0,1)}}function qr(t,e){var r=(parseFloat(t[0])%360+360)%360/360,i=Or(t[1]),n=Or(t[2]),o=n<=.5?n*(i+1):n+i-n*i,a=2*n-o;return Br(e=e||[],Dr(255*Rr(a,o,r+1/3)),Dr(255*Rr(a,o,r)),Dr(255*Rr(a,o,r-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Yr(t,e){var r=Xr(t);if(r){for(var i=0;i<3;i++)r[i]=e<0?r[i]*(1-e)|0:(255-r[i])*e+r[i]|0,255<r[i]?r[i]=255:r[i]<0&&(r[i]=0);return Zr(r,4===r.length?"rgba":"rgb")}}function jr(t,e,r){if(e&&e.length&&0<=t&&t<=1){r=r||[];var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),a=e[n],s=e[o],h=i-n;return r[0]=Dr(Fr(a[0],s[0],h)),r[1]=Dr(Fr(a[1],s[1],h)),r[2]=Dr(Fr(a[2],s[2],h)),r[3]=zr(Fr(a[3],s[3],h)),r}}var Vr=jr;function Ur(t,e,r){if(e&&e.length&&0<=t&&t<=1){var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),a=Xr(e[n]),s=Xr(e[o]),h=i-n,l=Zr([Dr(Fr(a[0],s[0],h)),Dr(Fr(a[1],s[1],h)),Dr(Fr(a[2],s[2],h)),zr(Fr(a[3],s[3],h))],"rgba");return r?{color:l,leftIndex:n,rightIndex:o,value:i}:l}}var Gr=Ur;function Zr(t,e){if(t&&t.length){var r=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(r+=","+t[3]),e+"("+r+")"}}function Kr(t,e){var r=Xr(t);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*e:0}var Qr=new Mr(100);function $r(t){if(N(t)){var e=Qr.get(t);return e||(e=Yr(t,-.1),Qr.put(t,e)),e}if(j(t)){var r=I({},t);return r.colorStops=L(t.colorStops,function(t){return{offset:t.offset,color:Yr(t.color,-.1)}}),r}return t}var Jr=Object.freeze({__proto__:null,parse:Xr,lift:Yr,toHex:function(t){var e=Xr(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)},fastLerp:jr,fastMapToColor:Vr,lerp:Ur,mapToColor:Gr,modifyHSL:function(t,e,r,i){var n,o=Xr(t);if(t)return o=function(t){if(t){var e,r,i,n,o,a=t[0]/255,s=t[1]/255,h=t[2]/255,l=Math.min(a,s,h),u=Math.max(a,s,h),c=u-l,p=(u+l)/2;0==c?r=e=0:(r=p<.5?c/(u+l):c/(2-u-l),i=((u-a)/6+c/2)/c,n=((u-s)/6+c/2)/c,o=((u-h)/6+c/2)/c,a===u?e=o-n:s===u?e=1/3+i-o:h===u&&(e=2/3+n-i),e<0&&(e+=1),1<e&&--e);var f=[360*e,r,p];return null!=t[3]&&f.push(t[3]),f}}(o),null!=e&&(o[0]=(n=e,(n=Math.round(n))<0?0:360<n?360:n)),null!=r&&(o[1]=Or(r)),null!=i&&(o[2]=Or(i)),Zr(qr(o),"rgba")},modifyAlpha:function(t,e){var r=Xr(t);if(r&&null!=e)return r[3]=zr(e),Zr(r,"rgba")},stringify:Zr,lum:Kr,random:function(){return Zr([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},liftColor:$r}),ti=Math.round;function ei(t){var e,r;return t&&"transparent"!==t?"string"==typeof t&&-1<t.indexOf("rgba")&&((r=Xr(t))&&(t="rgb("+r[0]+","+r[1]+","+r[2]+")",e=r[3])):t="none",{color:t,opacity:null==e?1:e}}var ri=1e-4;function ii(t){return t<ri&&-ri<t}function ni(t){return ti(1e3*t)/1e3}function oi(t){return ti(1e4*t)/1e4}var ai={left:"start",right:"end",center:"middle",middle:"middle"};function si(t){return t&&!!t.image}function hi(t){return si(t)||(e=t)&&e.svgElement;var e}function li(t){return"linear"===t.type}function ui(t){return"radial"===t.type}function ci(t){return t&&("linear"===t.type||"radial"===t.type)}function pi(t){return"url(#"+t+")"}function fi(t){var e=t.getGlobalScale(),r=Math.max(e[0],e[1]);return Math.max(Math.ceil(Math.log(r)/Math.log(10)),1)}function di(t){var e=t.x||0,r=t.y||0,i=(t.rotation||0)*pt,n=Z(t.scaleX,1),o=Z(t.scaleY,1),a=t.skewX||0,s=t.skewY||0,h=[];return(e||r)&&h.push("translate("+e+"px,"+r+"px)"),i&&h.push("rotate("+i+")"),1===n&&1===o||h.push("scale("+n+","+o+")"),(a||s)&&h.push("skew("+ti(a*pt)+"deg, "+ti(s*pt)+"deg)"),h.join(" ")}var yi=c.hasGlobalWindow&&B(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:"undefined"!=typeof Buffer?function(t){return Buffer.from(t).toString("base64")}:function(t){return null},vi=Array.prototype.slice;function gi(t,e,r){return(e-t)*r+t}function _i(t,e,r,i){for(var n=e.length,o=0;o<n;o++)t[o]=gi(e[o],r[o],i);return t}function mi(t,e,r,i){for(var n=e.length,o=0;o<n;o++)t[o]=e[o]+r[o]*i;return t}function xi(t,e,r,i){for(var n=e.length,o=n&&e[0].length,a=0;a<n;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+r[a][s]*i}return t}function wi(t){if(A(t)){var e=t.length;if(A(t[0])){for(var r=[],i=0;i<e;i++)r.push(vi.call(t[i]));return r}return vi.call(t)}return t}function bi(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Si(t){return 4===t||5===t}function ki(t){return 1===t||2===t}var Ti=[0,0,0,0],Ci=(Pi.prototype.isFinished=function(){return this._finished},Pi.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},Pi.prototype.needsAnimate=function(){return 1<=this.keyframes.length},Pi.prototype.getAdditiveTrack=function(){return this._additiveTrack},Pi.prototype.addKeyframe=function(t,e,r){this._needsSort=!0;var i,n,o,a,s=this.keyframes,h=s.length,l=!1,u=6,c=e;A(e)?(1==(u=i=A((a=e)&&a[0])?2:1)&&!H(e[0])||2==i&&!H(e[0][0]))&&(l=!0):H(e)&&!U(e)?u=0:N(e)?isNaN(+e)?(n=Xr(e))&&(c=n,u=3):u=0:j(e)&&((o=I({},c)).colorStops=L(e.colorStops,function(t){return{offset:t.offset,color:Xr(t.color)}}),li(e)?u=4:ui(e)&&(u=5),c=o),0===h?this.valType=u:u===this.valType&&6!==u||(l=!0),this.discrete=this.discrete||l;var p={time:t,value:c,rawValue:e,percent:0};return r&&(p.easing=r,p.easingFunc=B(r)?r:tr[r]||br(r)),s.push(p),p},Pi.prototype.prepare=function(t,e){var r=this.keyframes;this._needsSort&&r.sort(function(t,e){return t.time-e.time});for(var i=this.valType,n=r.length,o=r[n-1],a=this.discrete,s=ki(i),h=Si(i),l=0;l<n;l++){var u=r[l],c=u.value,p=o.value;u.percent=u.time/t,a||(s&&l!==n-1?function(t,e,r){var i=t,n=e;if(i.push&&n.push){var o=i.length,a=n.length;if(o!==a)if(a<o)i.length=a;else for(var s=o;s<a;s++)i.push(1===r?n[s]:vi.call(n[s]));for(var h=i[0]&&i[0].length,s=0;s<i.length;s++)if(1===r)isNaN(i[s])&&(i[s]=n[s]);else for(var l=0;l<h;l++)isNaN(i[s][l])&&(i[s][l]=n[s][l])}}(c,p,i):h&&function(t,e){for(var r=t.length,i=e.length,n=i<r?e:t,o=Math.min(r,i),a=n[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(r,i);s++)n.push({offset:a.offset,color:a.color.slice()})}(c.colorStops,p.colorStops))}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var f=r[0].value,l=0;l<n;l++)0===i?r[l].additiveValue=r[l].value-f:3===i?r[l].additiveValue=mi([],r[l].value,f,-1):ki(i)&&(r[l].additiveValue=(1===i?mi:xi)([],r[l].value,f,-1))}},Pi.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var r,i,n,o,a,s,h,l,u,c=null!=this._additiveTrack,p=c?"additiveValue":"value",f=this.valType,d=this.keyframes,y=d.length,v=this.propName,g=3===f,_=this._lastFr,m=Math.min;if(1===y)r=i=d[0];else{if(e<0)x=0;else if(e<this._lastFrP){for(var x=m(_+1,y-1);0<=x&&!(d[x].percent<=e);x--);x=m(x,y-2)}else{for(x=_;x<y&&!(d[x].percent>e);x++);x=m(x-1,y-2)}i=d[x+1],r=d[x]}r&&i&&(this._lastFr=x,this._lastFrP=e,n=i.percent-r.percent,o=0==n?1:m((e-r.percent)/n,1),i.easingFunc&&(o=i.easingFunc(o)),a=c?this._additiveValue:g?Ti:t[v],!ki(f)&&!g||a||(a=this._additiveValue=[]),this.discrete?t[v]=o<1?r.rawValue:i.rawValue:ki(f)?(1===f?_i:function(t,e,r,i){for(var n=e.length,o=n&&e[0].length,a=0;a<n;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=gi(e[a][s],r[a][s],i)}return t})(a,r[p],i[p],o):Si(f)?(s=r[p],h=i[p],l=4===f,t[v]={type:l?"linear":"radial",x:gi(s.x,h.x,o),y:gi(s.y,h.y,o),colorStops:L(s.colorStops,function(t,e){var r=h.colorStops[e];return{offset:gi(t.offset,r.offset,o),color:bi(_i([],t.color,r.color,o))}}),global:h.global},l?(t[v].x2=gi(s.x2,h.x2,o),t[v].y2=gi(s.y2,h.y2,o)):t[v].r=gi(s.r,h.r,o)):g?(_i(a,r[p],i[p],o),c||(t[v]=bi(a))):(u=gi(r[p],i[p],o),c?this._additiveValue=u:t[v]=u),c&&this._addToTarget(t))}},Pi.prototype._addToTarget=function(t){var e=this.valType,r=this.propName,i=this._additiveValue;0===e?t[r]=t[r]+i:3===e?(Xr(t[r],Ti),mi(Ti,Ti,i,1),t[r]=bi(Ti)):1===e?mi(t[r],t[r],i,1):2===e&&xi(t[r],t[r],i,1)},Pi);function Pi(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}var Mi=(Ai.prototype.getMaxTime=function(){return this._maxTime},Ai.prototype.getDelay=function(){return this._delay},Ai.prototype.getLoop=function(){return this._loop},Ai.prototype.getTarget=function(){return this._target},Ai.prototype.changeTarget=function(t){this._target=t},Ai.prototype.when=function(t,e,r){return this.whenWithKeys(t,e,F(e),r)},Ai.prototype.whenWithKeys=function(t,e,r,i){for(var n=this._tracks,o=0;o<r.length;o++){var a=r[o],s=n[a];if(!s){s=n[a]=new Ci(a);var h,l,u=void 0,c=this._getAdditiveTrack(a);if(c?(u=(l=(h=c.keyframes)[h.length-1])&&l.value,3===c.valType&&u&&(u=bi(u))):u=this._target[a],null==u)continue;0<t&&s.addKeyframe(0,wi(u),i),this._trackKeys.push(a)}s.addKeyframe(t,wi(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},Ai.prototype.pause=function(){this._clip.pause(),this._paused=!0},Ai.prototype.resume=function(){this._clip.resume(),this._paused=!1},Ai.prototype.isPaused=function(){return!!this._paused},Ai.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},Ai.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,r=0;r<e;r++)t[r].call(this)},Ai.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var r=0;r<e.length;r++)e[r].call(this)},Ai.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,r=0;r<e.length;r++)t[e[r]].setFinished()},Ai.prototype._getAdditiveTrack=function(t){var e,r=this._additiveAnimators;if(r)for(var i=0;i<r.length;i++){var n=r[i].getTrack(t);n&&(e=n)}return e},Ai.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,o=this,a=[],r=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var n,s=this._trackKeys[i],h=this._tracks[s],l=this._getAdditiveTrack(s),u=h.keyframes,c=u.length;h.prepare(r,l),h.needsAnimate()&&(!this._allowDiscrete&&h.discrete?((n=u[c-1])&&(o._target[h.propName]=n.rawValue),h.setFinished()):a.push(h))}return a.length||this._force?(e=new Sr({life:r,loop:this._loop,delay:this._delay||0,onframe:function(t){o._started=2;var e=o._additiveAnimators;if(e){for(var r=!1,i=0;i<e.length;i++)if(e[i]._clip){r=!0;break}r||(o._additiveAnimators=null)}for(i=0;i<a.length;i++)a[i].step(o._target,t);var n=o._onframeCbs;if(n)for(i=0;i<n.length;i++)n[i](o._target,t)},ondestroy:function(){o._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},Ai.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},Ai.prototype.delay=function(t){return this._delay=t,this},Ai.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},Ai.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},Ai.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},Ai.prototype.getClip=function(){return this._clip},Ai.prototype.getTrack=function(t){return this._tracks[t]},Ai.prototype.getTracks=function(){var e=this;return L(this._trackKeys,function(t){return e._tracks[t]})},Ai.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var r=this._tracks,i=this._trackKeys,n=0;n<t.length;n++){var o=r[t[n]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,n=0;n<i.length;n++)if(!r[i[n]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},Ai.prototype.saveTo=function(t,e,r){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var n,o,a=e[i],s=this._tracks[a];s&&!s.isFinished()&&(o=(n=s.keyframes)[r?0:n.length-1])&&(t[a]=wi(o.rawValue))}}},Ai.prototype.__changeFinalValue=function(t,e){e=e||F(t);for(var r=0;r<e.length;r++){var i,n,o=e[r],a=this._tracks[o];a&&1<(i=a.keyframes).length&&(n=i.pop(),a.addKeyframe(n.time,t[o]),a.prepare(this._maxTime,a.getAdditiveTrack()))}},Ai);function Ai(t,e,r,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?b("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=r)}function Li(){return(new Date).getTime()}var Di,zi=(yt(Ii,Di=Ft),Ii.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},Ii.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},Ii.prototype.removeClip=function(t){var e,r;t.animation&&(e=t.prev,r=t.next,e?e.next=r:this._head=r,r?r.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},Ii.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},Ii.prototype.update=function(t){for(var e=Li()-this._pausedTime,r=e-this._time,i=this._head;i;)var n=i.next,i=(i.step(e,r)&&(i.ondestroy(),this.removeClip(i)),n);this._time=e,t||(this.trigger("frame",r),this.stage.update&&this.stage.update())},Ii.prototype._startLoop=function(){var e=this;this._running=!0,Je(function t(){e._running&&(Je(t),e._paused||e.update())})},Ii.prototype.start=function(){this._running||(this._time=Li(),this._pausedTime=0,this._startLoop())},Ii.prototype.stop=function(){this._running=!1},Ii.prototype.pause=function(){this._paused||(this._pauseStart=Li(),this._paused=!0)},Ii.prototype.resume=function(){this._paused&&(this._pausedTime+=Li()-this._pauseStart,this._paused=!1)},Ii.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},Ii.prototype.isFinished=function(){return null==this._head},Ii.prototype.animate=function(t,e){e=e||{},this.start();var r=new Mi(t,e.loop);return this.addAnimator(r),r},Ii);function Ii(t){var e=Di.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,t=t||{},e.stage=t.stage||{},e}var Oi,Ri,Fi=c.domSupported,Bi=(Ri={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:Oi=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:L(Oi,function(t){var e=t.replace("mouse","pointer");return Ri.hasOwnProperty(e)?e:t})}),Ni=["mousemove","mouseup"],Hi=["pointermove","pointerup"],Ei=!1;function Wi(t){var e=t.pointerType;return"pen"===e||"touch"===e}function Xi(t){t&&(t.zrByTouch=!0)}function qi(t,e){for(var r=e,i=!1;r&&9!==r.nodeType&&!(i=r.domBelongToZr||r!==e&&r===t.painterRoot);)r=r.parentNode;return i}var Yi=function(t,e){this.stopPropagation=ct,this.stopImmediatePropagation=ct,this.preventDefault=ct,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},ji={mousedown:function(t){t=$t(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=$t(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=$t(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){qi(this,(t=$t(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Ei=!0,t=$t(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Ei||(t=$t(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Xi(t=$t(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),ji.mousemove.call(this,t),ji.mousedown.call(this,t)},touchmove:function(t){Xi(t=$t(this.dom,t)),this.handler.processGesture(t,"change"),ji.mousemove.call(this,t)},touchend:function(t){Xi(t=$t(this.dom,t)),this.handler.processGesture(t,"end"),ji.mouseup.call(this,t),new Date-this.__lastTouchMoment<300&&ji.click.call(this,t)},pointerdown:function(t){ji.mousedown.call(this,t)},pointermove:function(t){Wi(t)||ji.mousemove.call(this,t)},pointerup:function(t){ji.mouseup.call(this,t)},pointerout:function(t){Wi(t)||ji.mouseout.call(this,t)}};O(["click","dblclick","contextmenu"],function(e){ji[e]=function(t){t=$t(this.dom,t),this.trigger(e,t)}});var Vi={pointermove:function(t){Wi(t)||Vi.mousemove.call(this,t)},pointerup:function(t){Vi.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function Ui(i,n){var o=n.domHandlers;c.pointerEventsSupported?O(Bi.pointer,function(e){Zi(n,e,function(t){o[e].call(i,t)})}):(c.touchEventsSupported&&O(Bi.touch,function(r){Zi(n,r,function(t){var e;o[r].call(i,t),(e=n).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),O(Bi.mouse,function(e){Zi(n,e,function(t){t=Qt(t),n.touching||o[e].call(i,t)})}))}function Gi(n,o){function t(i){Zi(o,i,function(t){var e,r;t=Qt(t),qi(n,t.target)||(r=t,t=$t((e=n).dom,new Yi(e,r),!0),o.domHandlers[i].call(n,t))},{capture:!0})}c.pointerEventsSupported?O(Hi,t):c.touchEventsSupported||O(Ni,t)}function Zi(t,e,r,i){var n,o,a,s;t.mounted[e]=r,t.listenerOpts[e]=i,n=t.domTarget,o=e,a=r,s=i,n.addEventListener(o,a,s)}function Ki(t){var e,r,i,n,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,i=o[r=a],n=t.listenerOpts[a],e.removeEventListener(r,i,n));t.mounted={}}var Qi,$i=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},Ji=(yt(tn,Qi=Ft),tn.prototype.dispose=function(){Ki(this._localHandlerScope),Fi&&Ki(this._globalHandlerScope)},tn.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},tn.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,Fi&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?Gi(this,e):Ki(e))},tn);function tn(t,e){var r=Qi.call(this)||this;return r.__pointerCapturing=!1,r.dom=t,r.painterRoot=e,r._localHandlerScope=new $i(t,ji),Fi&&(r._globalHandlerScope=new $i(document,Vi)),Ui(r,r._localHandlerScope),r}var en=1;c.hasGlobalWindow&&(en=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var rn=en,nn="#333",on="#ccc",an=oe;function sn(t){return 5e-5<t||t<-5e-5}var hn,ln=[],un=[],cn=ne(),pn=Math.abs,fn=(dn.prototype.getLocalTransform=function(t){return dn.getLocalTransform(this,t)},dn.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},dn.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},dn.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},dn.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},dn.prototype.needLocalTransform=function(){return sn(this.rotation)||sn(this.x)||sn(this.y)||sn(this.scaleX-1)||sn(this.scaleY-1)||sn(this.skewX)||sn(this.skewY)},dn.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),r=this.transform;e||t?(r=r||ne(),e?this.getLocalTransform(r):an(r),t&&(e?se(r,t,r):ae(r,t)),this.transform=r,this._resolveGlobalScaleRatio(r)):r&&(an(r),this.invTransform=null)},dn.prototype._resolveGlobalScaleRatio=function(t){var e,r,i,n,o=this.globalScaleRatio;null!=o&&1!==o&&(this.getGlobalScale(ln),i=((ln[0]-(e=ln[0]<0?-1:1))*o+e)/ln[0]||0,n=((ln[1]-(r=ln[1]<0?-1:1))*o+r)/ln[1]||0,t[0]*=i,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||ne(),ce(this.invTransform,t)},dn.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},dn.prototype.setLocalTransform=function(t){var e,r,i,n;t&&(n=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),r=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(r),n=Math.sqrt(n),this.skewX=r,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=n,this.scaleY=i,this.originX=0,this.originY=0)},dn.prototype.decomposeTransform=function(){var t,e,r,i;this.transform&&(t=this.parent,e=this.transform,t&&t.transform&&(t.invTransform=t.invTransform||ne(),se(un,t.invTransform,e),e=un),r=this.originX,i=this.originY,(r||i)&&(cn[4]=r,cn[5]=i,se(un,e,cn),un[4]-=r,un[5]-=i,e=un),this.setLocalTransform(e))},dn.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},dn.prototype.transformCoordToLocal=function(t,e){var r=[t,e],i=this.invTransform;return i&&At(r,r,i),r},dn.prototype.transformCoordToGlobal=function(t,e){var r=[t,e],i=this.transform;return i&&At(r,r,i),r},dn.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<pn(t[0]-1)&&1e-10<pn(t[3]-1)?Math.sqrt(pn(t[0]*t[3]-t[2]*t[1])):1},dn.prototype.copyTransform=function(t){vn(this,t)},dn.getLocalTransform=function(t,e){e=e||[];var r,i,n=t.originX||0,o=t.originY||0,a=t.scaleX,s=t.scaleY,h=t.anchorX,l=t.anchorY,u=t.rotation||0,c=t.x,p=t.y,f=t.skewX?Math.tan(t.skewX):0,d=t.skewY?Math.tan(-t.skewY):0;return n||o||h||l?(r=n+h,i=o+l,e[4]=-r*a-f*i*s,e[5]=-i*s-d*r*a):e[4]=e[5]=0,e[0]=a,e[3]=s,e[1]=d*a,e[2]=f*s,u&&le(e,e,u),e[4]+=n+c,e[5]+=o+p,e},dn.initDefaultProps=((hn=dn.prototype).scaleX=hn.scaleY=hn.globalScaleRatio=1,void(hn.x=hn.y=hn.originX=hn.originY=hn.skewX=hn.skewY=hn.rotation=hn.anchorX=hn.anchorY=0)),dn);function dn(){}var yn=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function vn(t,e){for(var r=0;r<yn.length;r++){var i=yn[r];t[i]=e[i]}}var gn={};function _n(t,e){var r=gn[e=e||E],i=(r=r||(gn[e]=new Mr(500))).get(t);return null==i&&(i=f.measureText(t,e).width,r.put(t,i)),i}function mn(t,e,r,i){var n=_n(t,e),o=bn(e),a=xn(0,n,r),s=wn(0,o,i);return new Se(a,s,n,o)}function xn(t,e,r){return"right"===r?t-=e:"center"===r&&(t-=e/2),t}function wn(t,e,r){return"middle"===r?t-=e/2:"bottom"===r&&(t-=e),t}function bn(t){return _n("国",t)}function Sn(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}var kn,Tn="__zr_normal__",Cn=yn.concat(["ignore"]),Pn=D(yn,function(t,e){return t[e]=!0,t},{ignore:!1}),Mn={},An=new Se(0,0,0,0),Ln=(Dn.prototype._init=function(t){this.attr(t)},Dn.prototype.drift=function(t,e,r){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},Dn.prototype.beforeUpdate=function(){},Dn.prototype.afterUpdate=function(){},Dn.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},Dn.prototype.updateInnerText=function(t){var e,r,i,n,o,a,s,h,l,u,c,p,f,d,y,v,g,_=this._textContent;!_||_.ignore&&!t||(this.textConfig||(this.textConfig={}),r=(e=this.textConfig).local,o=n=void 0,a=!1,(i=_.innerTransformable).parent=r?this:null,c=!1,i.copyTransform(_),null!=e.position&&(s=An,e.layoutRect?s.copy(e.layoutRect):s.copy(this.getBoundingRect()),r||s.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Mn,e,s):function(t,e,r){var i=e.position||"inside",n=null!=e.distance?e.distance:5,o=r.height,a=r.width,s=o/2,h=r.x,l=r.y,u="left",c="top";if(i instanceof Array)h+=Sn(i[0],r.width),l+=Sn(i[1],r.height),c=u=null;else switch(i){case"left":h-=n,l+=s,u="right",c="middle";break;case"right":h+=n+a,l+=s,c="middle";break;case"top":h+=a/2,l-=n,u="center",c="bottom";break;case"bottom":h+=a/2,l+=o+n,u="center";break;case"inside":h+=a/2,l+=s,u="center",c="middle";break;case"insideLeft":h+=n,l+=s,c="middle";break;case"insideRight":h+=a-n,l+=s,u="right",c="middle";break;case"insideTop":h+=a/2,l+=n,u="center";break;case"insideBottom":h+=a/2,l+=o-n,u="center",c="bottom";break;case"insideTopLeft":h+=n,l+=n;break;case"insideTopRight":h+=a-n,l+=n,u="right";break;case"insideBottomLeft":h+=n,l+=o-n,c="bottom";break;case"insideBottomRight":h+=a-n,l+=o-n,u="right",c="bottom"}(t=t||{}).x=h,t.y=l,t.align=u,t.verticalAlign=c}(Mn,e,s),i.x=Mn.x,i.y=Mn.y,n=Mn.align,o=Mn.verticalAlign,(h=e.origin)&&null!=e.rotation&&(u=l=void 0,u="center"===h?(l=.5*s.width,.5*s.height):(l=Sn(h[0],s.width),Sn(h[1],s.height)),c=!0,i.originX=-i.x+l+(r?0:s.x),i.originY=-i.y+u+(r?0:s.y))),null!=e.rotation&&(i.rotation=e.rotation),(p=e.offset)&&(i.x+=p[0],i.y+=p[1],c||(i.originX=-p[0],i.originY=-p[1])),f=null==e.inside?"string"==typeof e.position&&0<=e.position.indexOf("inside"):e.inside,d=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),g=v=y=void 0,f&&this.canBeInsideText()?(y=e.insideFill,v=e.insideStroke,null!=y&&"auto"!==y||(y=this.getInsideTextFill()),null!=v&&"auto"!==v||(v=this.getInsideTextStroke(y),g=!0)):(y=e.outsideFill,v=e.outsideStroke,null!=y&&"auto"!==y||(y=this.getOutsideFill()),null!=v&&"auto"!==v||(v=this.getOutsideStroke(y),g=!0)),(y=y||"#000")===d.fill&&v===d.stroke&&g===d.autoStroke&&n===d.align&&o===d.verticalAlign||(a=!0,d.fill=y,d.stroke=v,d.autoStroke=g,d.align=n,d.verticalAlign=o,_.setDefaultTextStyle(d)),_.__dirty|=Ve,a&&_.dirtyStyle(!0))},Dn.prototype.canBeInsideText=function(){return!0},Dn.prototype.getInsideTextFill=function(){return"#fff"},Dn.prototype.getInsideTextStroke=function(t){return"#000"},Dn.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?on:nn},Dn.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),r="string"==typeof e&&Xr(e),i=(r=r||[255,255,255,1])[3],n=this.__zr.isDarkMode(),o=0;o<3;o++)r[o]=r[o]*i+(n?0:255)*(1-i);return r[3]=1,Zr(r,"rgba")},Dn.prototype.traverse=function(t,e){},Dn.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},I(this.extra,e)):this[t]=e},Dn.prototype.hide=function(){this.ignore=!0,this.markRedraw()},Dn.prototype.show=function(){this.ignore=!1,this.markRedraw()},Dn.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(W(t))for(var r=F(t),i=0;i<r.length;i++){var n=r[i];this.attrKV(n,t[n])}return this.markRedraw(),this},Dn.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,r=0;r<this.animators.length;r++){var i,n,o=this.animators[r],a=o.__fromStateTransition;o.getLoop()||a&&a!==Tn||(n=(i=o.targetName)?e[i]:e,o.saveTo(n))}},Dn.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Cn)},Dn.prototype._savePrimaryToNormal=function(t,e,r){for(var i=0;i<r.length;i++){var n=r[i];null==t[n]||n in e||(e[n]=this[n])}},Dn.prototype.hasState=function(){return 0<this.currentStates.length},Dn.prototype.getState=function(t){return this.states[t]},Dn.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},Dn.prototype.clearStates=function(t){this.useState(Tn,!1,t)},Dn.prototype.useState=function(t,e,r,i){var n=t===Tn;if(this.hasState()||!n){var o,a=this.currentStates,s=this.stateTransition;if(!(0<=P(a,t))||!e&&1!==a.length){if(this.stateProxy&&!n&&(o=this.stateProxy(t)),(o=o||this.states&&this.states[t])||n){n||this.saveCurrentToNormalState(o);var h=!!(o&&o.hoverLayer||i);h&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,o,this._normalState,e,!r&&!this.__inHover&&s&&0<s.duration,s);var l=this._textContent,u=this._textGuide;return l&&l.useState(t,e,r,h),u&&u.useState(t,e,r,h),n?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Ve),o}b("State "+t+" not exists.")}}},Dn.prototype.useStates=function(t,e,r){if(t.length){var i=[],n=this.currentStates,o=t.length,a=o===n.length;if(a)for(var s=0;s<o;s++)if(t[s]!==n[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var h=t[s],l=void 0;this.stateProxy&&(l=this.stateProxy(h,t)),(l=l||this.states[h])&&i.push(l)}var u=i[o-1],c=!!(u&&u.hoverLayer||r);c&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(i),f=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&f&&0<f.duration,f);var d=this._textContent,y=this._textGuide;d&&d.useStates(t,e,c),y&&y.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Ve)}else this.clearStates()},Dn.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},Dn.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},Dn.prototype.removeState=function(t){var e,r=P(this.currentStates,t);0<=r&&((e=this.currentStates.slice()).splice(r,1),this.useStates(e))},Dn.prototype.replaceState=function(t,e,r){var i=this.currentStates.slice(),n=P(i,t),o=0<=P(i,e);0<=n?o?i.splice(n,1):i[n]=e:r&&!o&&i.push(e),this.useStates(i)},Dn.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},Dn.prototype._mergeStates=function(t){for(var e,r={},i=0;i<t.length;i++){var n=t[i];I(r,n),n.textConfig&&I(e=e||{},n.textConfig)}return e&&(r.textConfig=e),r},Dn.prototype._applyStateObj=function(t,e,r,i,n,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=I({},i?this.textConfig:r.textConfig),I(this.textConfig,e.textConfig)):a&&r.textConfig&&(this.textConfig=r.textConfig);for(var s={},h=!1,l=0;l<Cn.length;l++){var u=Cn[l],c=n&&Pn[u];e&&null!=e[u]?c?(h=!0,s[u]=e[u]):this[u]=e[u]:a&&null!=r[u]&&(c?(h=!0,s[u]=r[u]):this[u]=r[u])}if(!n)for(l=0;l<this.animators.length;l++){var p=this.animators[l],f=p.targetName;p.getLoop()||p.__changeFinalValue(f?(e||r)[f]:e||r)}h&&this._transitionState(t,s,o)},Dn.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},Dn.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},Dn.prototype.getClipPath=function(){return this._clipPath},Dn.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},Dn.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},Dn.prototype.getTextContent=function(){return this._textContent},Dn.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new fn,this._attachComponent(t),this._textContent=t,this.markRedraw())},Dn.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),I(this.textConfig,t),this.markRedraw()},Dn.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},Dn.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},Dn.prototype.getTextGuideLine=function(){return this._textGuide},Dn.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},Dn.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},Dn.prototype.markRedraw=function(){this.__dirty|=Ve;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},Dn.prototype.dirty=function(){this.markRedraw()},Dn.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,r=this._textGuide;e&&(e.__inHover=t),r&&(r.__inHover=t)},Dn.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.addAnimator(e[r]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},Dn.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.removeAnimator(e[r]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},Dn.prototype.animate=function(t,e,r){var i=t?this[t]:this,n=new Mi(i,e,r);return t&&(n.targetName=t),this.addAnimator(n,t),n},Dn.prototype.addAnimator=function(r,t){var e=this.__zr,i=this;r.during(function(){i.updateDuringAnimation(t)}).done(function(){var t=i.animators,e=P(t,r);0<=e&&t.splice(e,1)}),this.animators.push(r),e&&e.animation.addAnimator(r),e&&e.wakeUp()},Dn.prototype.updateDuringAnimation=function(t){this.markRedraw()},Dn.prototype.stopAnimation=function(t,e){for(var r=this.animators,i=r.length,n=[],o=0;o<i;o++){var a=r[o];t&&t!==a.scope?n.push(a):a.stop(e)}return this.animators=n,this},Dn.prototype.animateTo=function(t,e,r){In(this,t,e,r)},Dn.prototype.animateFrom=function(t,e,r){In(this,t,e,r,!0)},Dn.prototype._transitionState=function(t,e,r,i){for(var n=In(this,e,r,i),o=0;o<n.length;o++)n[o].__fromStateTransition=t},Dn.prototype.getBoundingRect=function(){return null},Dn.prototype.getPaintRect=function(){return null},Dn.initDefaultProps=((kn=Dn.prototype).type="element",kn.name="",kn.ignore=kn.silent=kn.isGroup=kn.draggable=kn.dragging=kn.ignoreClip=kn.__inHover=!1,kn.__dirty=Ve,void(Object.defineProperty&&(zn("position","_legacyPos","x","y"),zn("scale","_legacyScale","scaleX","scaleY"),zn("origin","_legacyOrigin","originX","originY")))),Dn);function Dn(t){this.id=w(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}function zn(t,e,r,i){function n(e,t){Object.defineProperty(t,0,{get:function(){return e[r]},set:function(t){e[r]=t}}),Object.defineProperty(t,1,{get:function(){return e[i]},set:function(t){e[i]=t}})}Object.defineProperty(kn,t,{get:function(){var t;return this[e]||(t=this[e]=[],n(this,t)),this[e]},set:function(t){this[r]=t[0],this[i]=t[1],this[e]=t,n(this,t)}})}function In(t,e,r,i,n){var o=[];!function t(e,r,i,n,o,a,s,h){var l=F(n);var u=o.duration;var c=o.delay;var p=o.additive;var f=o.setToFinal;var d=!W(a);var y=e.animators;var v=[];for(var g=0;g<l.length;g++){var _=l[g],m=n[_];if(null!=m&&null!=i[_]&&(d||a[_]))if(!W(m)||A(m)||j(m))v.push(_);else{if(r){h||(i[_]=m,e.updateDuringAnimation(r));continue}t(e,_,i[_],m,o,a&&a[_],s,h)}else h||(i[_]=m,e.updateDuringAnimation(r),v.push(_))}var x=v.length;if(!p&&x)for(var w=0;w<y.length;w++){var b,S=y[w];S.targetName!==r||S.stopTracks(v)&&(b=P(y,S),y.splice(b,1))}o.force||(v=R(v,function(t){return e=n[t],r=i[t],!(e===r||A(e)&&A(r)&&function(t,e){var r=t.length;if(r!==e.length)return;for(var i=0;i<r;i++)if(t[i]!==e[i])return;return 1}(e,r));var e,r}),x=v.length);if(0<x||o.force&&!s.length){var k=void 0,T=void 0,C=void 0;if(h){T={},f&&(k={});for(var w=0;w<x;w++){var _=v[w];T[_]=i[_],f?k[_]=n[_]:i[_]=n[_]}}else if(f){C={};for(var w=0;w<x;w++){var _=v[w];C[_]=wi(i[_]),Rn(i,n,_)}}var S=new Mi(i,!1,!1,p?R(y,function(t){return t.targetName===r}):null);S.targetName=r,o.scope&&(S.scope=o.scope),f&&k&&S.whenWithKeys(0,k,v),C&&S.whenWithKeys(0,C,v),S.whenWithKeys(null==u?500:u,h?T:n,v).delay(c||0),e.addAnimator(S,r),s.push(S)}}(t,"",t,e,r=r||{},i,o,n);function a(){l=!0,--h<=0&&(l?u&&u():c&&c())}function s(){--h<=0&&(l?u&&u():c&&c())}var h=o.length,l=!1,u=r.done,c=r.aborted;h||u&&u(),0<o.length&&r.during&&o[0].during(function(t,e){r.during(e)});for(var p=0;p<o.length;p++){var f=o[p];f.done(a),f.aborted(s),r.force&&f.duration(r.duration),f.start(r.easing)}return o}function On(t,e,r){for(var i=0;i<r;i++)t[i]=e[i]}function Rn(t,e,r){if(A(e[r]))if(A(t[r])||(t[r]=[]),q(e[r])){var i=e[r].length;t[r].length!==i&&(t[r]=new e[r].constructor(i),On(t[r],e[r],i))}else{var n=e[r],o=t[r],a=n.length;if(A(n[0]))for(var s=n[0].length,h=0;h<a;h++)o[h]?On(o[h],n[h],s):o[h]=Array.prototype.slice.call(n[h]);else On(o,n,a);o.length=n.length}else t[r]=e[r]}M(Ln,Ft),M(Ln,fn);var Fn,Bn=(yt(Nn,Fn=Ln),Nn.prototype.childrenRef=function(){return this._children},Nn.prototype.children=function(){return this._children.slice()},Nn.prototype.childAt=function(t){return this._children[t]},Nn.prototype.childOfName=function(t){for(var e=this._children,r=0;r<e.length;r++)if(e[r].name===t)return e[r]},Nn.prototype.childCount=function(){return this._children.length},Nn.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},Nn.prototype.addBefore=function(t,e){var r,i;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(i=(r=this._children).indexOf(e))&&(r.splice(i,0,t),this._doAdd(t)),this},Nn.prototype.replace=function(t,e){var r=P(this._children,t);return 0<=r&&this.replaceAt(e,r),this},Nn.prototype.replaceAt=function(t,e){var r,i=this._children,n=i[e];return t&&t!==this&&t.parent!==this&&t!==n&&(i[e]=t,n.parent=null,(r=this.__zr)&&n.removeSelfFromZr(r),this._doAdd(t)),this},Nn.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},Nn.prototype.remove=function(t){var e=this.__zr,r=this._children,i=P(r,t);return i<0||(r.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},Nn.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,r=0;r<t.length;r++){var i=t[r];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},Nn.prototype.eachChild=function(t,e){for(var r=this._children,i=0;i<r.length;i++){var n=r[i];t.call(e,n,i)}return this},Nn.prototype.traverse=function(t,e){for(var r=0;r<this._children.length;r++){var i=this._children[r],n=t.call(e,i);i.isGroup&&!n&&i.traverse(t,e)}return this},Nn.prototype.addSelfToZr=function(t){Fn.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},Nn.prototype.removeSelfFromZr=function(t){Fn.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},Nn.prototype.getBoundingRect=function(t){for(var e=new Se(0,0,0,0),r=t||this._children,i=[],n=null,o=0;o<r.length;o++){var a,s,h=r[o];h.ignore||h.invisible||(a=h.getBoundingRect(),(s=h.getLocalTransform(i))?(Se.applyTransform(e,a,s),(n=n||e.clone()).union(e)):(n=n||a.clone()).union(a))}return n||e},Nn);function Nn(t){var e=Fn.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}Bn.prototype.type="group";var Hn={},En={};var Wn,Xn=(qn.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},qn.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},qn.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},qn.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return Kr(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,r=0,i=e.length,n=0;n<i;n++)r+=Kr(e[n].color,1);return(r/=i)<.4}return!1}(t))},qn.prototype.getBackgroundColor=function(){return this._backgroundColor},qn.prototype.setDarkMode=function(t){this._darkMode=t},qn.prototype.isDarkMode=function(){return this._darkMode},qn.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},qn.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},qn.prototype.flush=function(){this._disposed||this._flush(!1)},qn.prototype._flush=function(t){var e,r=Li();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=Li();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-r})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},qn.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},qn.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},qn.prototype.refreshHover=function(){this._needsRefreshHover=!0},qn.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},qn.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},qn.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},qn.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},qn.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},qn.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},qn.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},qn.prototype.on=function(t,e,r){return this._disposed||this.handler.on(t,e,r),this},qn.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},qn.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},qn.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Bn&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},qn.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete En[t])},qn);function qn(t,e,r){var i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,r=r||{},this.dom=e,this.id=t;var n=new Qe,o=r.renderer||"canvas";Hn[o]||(o=F(Hn)[0]),r.useDirtyRect=null!=r.useDirtyRect&&r.useDirtyRect;var a=new Hn[o](e,n,r,t),s=r.ssr||a.ssrOnly;this.storage=n,this.painter=a;var h,l=c.node||c.worker||s?null:new Ji(a.getViewportRoot(),a.root),u=r.useCoarsePointer;(null==u||"auto"===u?c.touchEventsSupported:!!u)&&(h=Z(r.pointerSize,44)),this.handler=new Oe(n,a,l,a.root,h),this.animation=new zi({stage:{update:s?null:function(){return i._flush(!0)}}}),s||this.animation.start()}function Yn(t,e){Hn[t]=e}function jn(t){if("function"==typeof Wn)return Wn(t)}var Vn="__zr_style_"+Math.round(10*Math.random()),Un={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},Gn={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};Un[Vn]=!0;var Zn,Kn,Qn=["z","z2","invisible"],$n=["invisible"],Jn=(yt(to,Zn=Ln),to.prototype._init=function(t){for(var e=F(t),r=0;r<e.length;r++){var i=e[r];"style"===i?this.useStyle(t[i]):Zn.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},to.prototype.beforeBrush=function(){},to.prototype.afterBrush=function(){},to.prototype.innerBeforeBrush=function(){},to.prototype.innerAfterBrush=function(){},to.prototype.shouldBePainted=function(t,e,r,i){var n=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,r){return eo.copy(t.getBoundingRect()),t.transform&&eo.applyTransform(t.transform),ro.width=e,ro.height=r,!eo.intersect(ro)}(this,t,e)||n&&!n[0]&&!n[3])return!1;if(r&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},to.prototype.contain=function(t,e){return this.rectContain(t,e)},to.prototype.traverse=function(t,e){t.call(e,this)},to.prototype.rectContain=function(t,e){var r=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(r[0],r[1])},to.prototype.getPaintRect=function(){var t,e,r,i,n,o,a,s=this._paintRect;return this._paintRect&&!this.__dirty||(t=this.transform,e=this.getBoundingRect(),i=(r=this.style).shadowBlur||0,n=r.shadowOffsetX||0,o=r.shadowOffsetY||0,s=this._paintRect||(this._paintRect=new Se(0,0,0,0)),t?Se.applyTransform(s,e,t):s.copy(e),(i||n||o)&&(s.width+=2*i+Math.abs(n),s.height+=2*i+Math.abs(o),s.x=Math.min(s.x,s.x+n-i),s.y=Math.min(s.y,s.y+o-i)),a=this.dirtyRectTolerance,s.isZero()||(s.x=Math.floor(s.x-a),s.y=Math.floor(s.y-a),s.width=Math.ceil(s.width+1+2*a),s.height=Math.ceil(s.height+1+2*a))),s},to.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new Se(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},to.prototype.getPrevPaintRect=function(){return this._prevPaintRect},to.prototype.animateStyle=function(t){return this.animate("style",t)},to.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},to.prototype.attrKV=function(t,e){"style"!==t?Zn.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},to.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:I(this.style,t),this.dirtyStyle(),this},to.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},to.prototype.dirty=function(){this.dirtyStyle()},to.prototype.styleChanged=function(){return!!(2&this.__dirty)},to.prototype.styleUpdated=function(){this.__dirty&=-3},to.prototype.createStyle=function(t){return ht(Un,t)},to.prototype.useStyle=function(t){t[Vn]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},to.prototype.isStyleObject=function(t){return t[Vn]},to.prototype._innerSaveToNormal=function(t){Zn.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,Qn)},to.prototype._applyStateObj=function(t,e,r,i,n,o){Zn.prototype._applyStateObj.call(this,t,e,r,i,n,o);var a,s=!(e&&i);if(e&&e.style?n?i?a=e.style:(a=this._mergeStyle(this.createStyle(),r.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),i?this.style:r.style),this._mergeStyle(a,e.style)):s&&(a=r.style),a)if(n){var h=this.style;if(this.style=this.createStyle(s?{}:h),s)for(var l=F(h),u=0;u<l.length;u++)(p=l[u])in a&&(a[p]=a[p],this.style[p]=h[p]);for(var c=F(a),u=0;u<c.length;u++){var p=c[u];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);for(var f=this.__inHover?$n:Qn,u=0;u<f.length;u++)p=f[u],e&&null!=e[p]?this[p]=e[p]:s&&null!=r[p]&&(this[p]=r[p])},to.prototype._mergeStates=function(t){for(var e,r=Zn.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var n=t[i];n.style&&(e=e||{},this._mergeStyle(e,n.style))}return e&&(r.style=e),r},to.prototype._mergeStyle=function(t,e){return I(t,e),t},to.prototype.getAnimationStyleProps=function(){return Gn},to.initDefaultProps=((Kn=to.prototype).type="displayable",Kn.invisible=!1,Kn.z=0,Kn.z2=0,Kn.zlevel=0,Kn.culling=!1,Kn.cursor="pointer",Kn.rectHover=!1,Kn.incremental=!1,Kn._rect=null,Kn.dirtyRectTolerance=0,void(Kn.__dirty=2|Ve)),to);function to(t){return Zn.call(this,t)||this}var eo=new Se(0,0,0,0),ro=new Se(0,0,0,0);var io=Math.min,no=Math.max,oo=Math.sin,ao=Math.cos,so=2*Math.PI,ho=vt(),lo=vt(),uo=vt();function co(t,e,r){if(0!==t.length){for(var i=t[0],n=i[0],o=i[0],a=i[1],s=i[1],h=1;h<t.length;h++)i=t[h],n=io(n,i[0]),o=no(o,i[0]),a=io(a,i[1]),s=no(s,i[1]);e[0]=n,e[1]=a,r[0]=o,r[1]=s}}function po(t,e,r,i,n,o){n[0]=io(t,r),n[1]=io(e,i),o[0]=no(t,r),o[1]=no(e,i)}var fo=[],yo=[];var vo={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},go=[],_o=[],mo=[],xo=[],wo=[],bo=[],So=Math.min,ko=Math.max,To=Math.cos,Co=Math.sin,Po=Math.abs,Mo=Math.PI,Ao=2*Mo,Lo="undefined"!=typeof Float32Array,Do=[];function zo(t){return Math.round(t/Mo*1e8)/1e8%2*Mo}var Io,Oo=(Ro.prototype.increaseVersion=function(){this._version++},Ro.prototype.getVersion=function(){return this._version},Ro.prototype.setScale=function(t,e,r){0<(r=r||0)&&(this._ux=Po(r/rn/t)||0,this._uy=Po(r/rn/e)||0)},Ro.prototype.setDPR=function(t){this.dpr=t},Ro.prototype.setContext=function(t){this._ctx=t},Ro.prototype.getContext=function(){return this._ctx},Ro.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},Ro.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},Ro.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(vo.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},Ro.prototype.lineTo=function(t,e){var r,i=Po(t-this._xi),n=Po(e-this._yi),o=i>this._ux||n>this._uy;return this.addData(vo.L,t,e),this._ctx&&o&&this._ctx.lineTo(t,e),o?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=i*i+n*n)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},Ro.prototype.bezierCurveTo=function(t,e,r,i,n,o){return this._drawPendingPt(),this.addData(vo.C,t,e,r,i,n,o),this._ctx&&this._ctx.bezierCurveTo(t,e,r,i,n,o),this._xi=n,this._yi=o,this},Ro.prototype.quadraticCurveTo=function(t,e,r,i){return this._drawPendingPt(),this.addData(vo.Q,t,e,r,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,r,i),this._xi=r,this._yi=i,this},Ro.prototype.arc=function(t,e,r,i,n,o){this._drawPendingPt(),Do[0]=i,Do[1]=n,function(t,e){var r=zo(t[0]);r<0&&(r+=Ao);var i=r-t[0],n=t[1];n+=i,!e&&Ao<=n-r?n=r+Ao:e&&Ao<=r-n?n=r-Ao:!e&&n<r?n=r+(Ao-zo(r-n)):e&&r<n&&(n=r-(Ao-zo(n-r))),t[0]=r,t[1]=n}(Do,o),i=Do[0];var a=(n=Do[1])-i;return this.addData(vo.A,t,e,r,r,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,r,i,n,o),this._xi=To(n)*r+t,this._yi=Co(n)*r+e,this},Ro.prototype.arcTo=function(t,e,r,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,r,i,n),this},Ro.prototype.rect=function(t,e,r,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,r,i),this.addData(vo.R,t,e,r,i),this},Ro.prototype.closePath=function(){this._drawPendingPt(),this.addData(vo.Z);var t=this._ctx,e=this._x0,r=this._y0;return t&&t.closePath(),this._xi=e,this._yi=r,this},Ro.prototype.fill=function(t){t&&t.fill(),this.toStatic()},Ro.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},Ro.prototype.len=function(){return this._len},Ro.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!Lo||(this.data=new Float32Array(e));for(var r=0;r<e;r++)this.data[r]=t[r];this._len=e},Ro.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,r=0,i=this._len,n=0;n<e;n++)r+=t[n].len();for(Lo&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+r)),n=0;n<e;n++)for(var o=t[n].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},Ro.prototype.addData=function(t,e,r,i,n,o,a,s,h){if(this._saveData){var l=this.data;this._len+arguments.length>l.length&&(this._expandData(),l=this.data);for(var u=0;u<arguments.length;u++)l[this._len++]=arguments[u]}},Ro.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},Ro.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},Ro.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array&&(t.length=this._len,Lo&&11<this._len&&(this.data=new Float32Array(t))))},Ro.prototype.getBoundingRect=function(){mo[0]=mo[1]=wo[0]=wo[1]=Number.MAX_VALUE,xo[0]=xo[1]=bo[0]=bo[1]=-Number.MAX_VALUE;for(var t,e,r,i,n,o,a,s,h,l,u,c,p,f,d=this.data,y=0,v=0,g=0,_=0,m=0;m<this._len;){var x=d[m++],w=1===m;switch(w&&(g=y=d[m],_=v=d[m+1]),x){case vo.M:y=g=d[m++],v=_=d[m++],wo[0]=g,wo[1]=_,bo[0]=g,bo[1]=_;break;case vo.L:po(y,v,d[m],d[m+1],wo,bo),y=d[m++],v=d[m++];break;case vo.C:!function(t,e,r,i,n,o,a,s,h,l){var u=yr,c=pr,p=u(t,r,n,a,fo);h[0]=1/0,h[1]=1/0,l[0]=-1/0,l[1]=-1/0;for(var f=0;f<p;f++){var d=c(t,r,n,a,fo[f]);h[0]=io(d,h[0]),l[0]=no(d,l[0])}for(p=u(e,i,o,s,yo),f=0;f<p;f++){var y=c(e,i,o,s,yo[f]);h[1]=io(y,h[1]),l[1]=no(y,l[1])}h[0]=io(t,h[0]),l[0]=no(t,l[0]),h[0]=io(a,h[0]),l[0]=no(a,l[0]),h[1]=io(e,h[1]),l[1]=no(e,l[1]),h[1]=io(s,h[1]),l[1]=no(s,l[1])}(y,v,d[m++],d[m++],d[m++],d[m++],d[m],d[m+1],wo,bo),y=d[m++],v=d[m++];break;case vo.Q:t=y,e=v,r=d[m++],i=d[m++],n=d[m],o=d[m+1],a=wo,s=bo,f=p=c=u=l=h=void 0,l=gr,u=no(io((h=mr)(t,r,n),1),0),c=no(io(h(e,i,o),1),0),p=l(t,r,n,u),f=l(e,i,o,c),a[0]=io(t,n,p),a[1]=io(e,o,f),s[0]=no(t,n,p),s[1]=no(e,o,f),y=d[m++],v=d[m++];break;case vo.A:var b=d[m++],S=d[m++],k=d[m++],T=d[m++],C=d[m++],P=d[m++]+C;m+=1;var M=!d[m++];w&&(g=To(C)*k+b,_=Co(C)*T+S),function(t,e,r,i,n,o,a,s,h){var l,u=Lt,c=Dt,p=Math.abs(n-o);if(p%so<1e-4&&1e-4<p)return s[0]=t-r,s[1]=e-i,h[0]=t+r,h[1]=e+i;ho[0]=ao(n)*r+t,ho[1]=oo(n)*i+e,lo[0]=ao(o)*r+t,lo[1]=oo(o)*i+e,u(s,ho,lo),c(h,ho,lo),(n%=so)<0&&(n+=so),(o%=so)<0&&(o+=so),o<n&&!a?o+=so:n<o&&a&&(n+=so),a&&(l=o,o=n,n=l);for(var f=0;f<o;f+=Math.PI/2)n<f&&(uo[0]=ao(f)*r+t,uo[1]=oo(f)*i+e,u(s,uo,s),c(h,uo,h))}(b,S,k,T,C,P,M,wo,bo),y=To(P)*k+b,v=Co(P)*T+S;break;case vo.R:po(g=y=d[m++],_=v=d[m++],g+d[m++],_+d[m++],wo,bo);break;case vo.Z:y=g,v=_}Lt(mo,mo,wo),Dt(xo,xo,bo)}return 0===m&&(mo[0]=mo[1]=xo[0]=xo[1]=0),new Se(mo[0],mo[1],xo[0]-mo[0],xo[1]-mo[1])},Ro.prototype._calculateLength=function(){var t=this.data,e=this._len,r=this._ux,i=this._uy,n=0,o=0,a=0,s=0;this._pathSegLen||(this._pathSegLen=[]);for(var h=this._pathSegLen,l=0,u=0,c=0;c<e;){var p=t[c++],f=1===c;f&&(a=n=t[c],s=o=t[c+1]);var d=-1;switch(p){case vo.M:n=a=t[c++],o=s=t[c++];break;case vo.L:var y=t[c++],v=(m=t[c++])-o;(Po(A=y-n)>r||Po(v)>i||c===e-1)&&(d=Math.sqrt(A*A+v*v),n=y,o=m);break;case vo.C:var g=t[c++],_=t[c++],y=t[c++],m=t[c++],x=t[c++],w=t[c++],d=function(t,e,r,i,n,o,a,s,h){for(var l=t,u=e,c=0,p=1/h,f=1;f<=h;f++){var d=f*p,y=pr(t,r,n,a,d),v=pr(e,i,o,s,d),g=y-l,_=v-u;c+=Math.sqrt(g*g+_*_),l=y,u=v}return c}(n,o,g,_,y,m,x,w,10),n=x,o=w;break;case vo.Q:d=function(t,e,r,i,n,o,a){for(var s=t,h=e,l=0,u=1/a,c=1;c<=a;c++){var p=c*u,f=gr(t,r,n,p),d=gr(e,i,o,p),y=f-s,v=d-h;l+=Math.sqrt(y*y+v*v),s=f,h=d}return l}(n,o,g=t[c++],_=t[c++],y=t[c++],m=t[c++],10),n=y,o=m;break;case vo.A:var b=t[c++],S=t[c++],k=t[c++],T=t[c++],C=t[c++],P=t[c++],M=P+C;c+=1,f&&(a=To(C)*k+b,s=Co(C)*T+S),d=ko(k,T)*So(Ao,Math.abs(P)),n=To(M)*k+b,o=Co(M)*T+S;break;case vo.R:a=n=t[c++],s=o=t[c++],d=2*t[c++]+2*t[c++];break;case vo.Z:var A=a-n,v=s-o;d=Math.sqrt(A*A+v*v),n=a,o=s}0<=d&&(l+=h[u++]=d)}return this._pathLen=l},Ro.prototype.rebuildPath=function(t,e){var r,i,n,o,a,s,h,l,u,c,p=this.data,f=this._ux,d=this._uy,y=this._len,v=e<1,g=0,_=0,m=0;if(!v||(this._pathSegLen||this._calculateLength(),h=this._pathSegLen,l=e*this._pathLen))t:for(var x=0;x<y;){var w=p[x++],b=1===x;switch(b&&(r=n=p[x],i=o=p[x+1]),w!==vo.L&&0<m&&(t.lineTo(u,c),m=0),w){case vo.M:r=n=p[x++],i=o=p[x++],t.moveTo(n,o);break;case vo.L:a=p[x++],s=p[x++];var S=Po(a-n),k=Po(s-o);if(f<S||d<k){if(v){if(l<g+(j=h[_++])){var T=(l-g)/j;t.lineTo(n*(1-T)+a*T,o*(1-T)+s*T);break t}g+=j}t.lineTo(a,s),n=a,o=s,m=0}else{var C=S*S+k*k;m<C&&(u=a,c=s,m=C)}break;case vo.C:var P=p[x++],M=p[x++],A=p[x++],L=p[x++],D=p[x++],z=p[x++];if(v){if(l<g+(j=h[_++])){vr(n,P,A,D,T=(l-g)/j,go),vr(o,M,L,z,T,_o),t.bezierCurveTo(go[1],_o[1],go[2],_o[2],go[3],_o[3]);break t}g+=j}t.bezierCurveTo(P,M,A,L,D,z),n=D,o=z;break;case vo.Q:if(P=p[x++],M=p[x++],A=p[x++],L=p[x++],v){if(l<g+(j=h[_++])){xr(n,P,A,T=(l-g)/j,go),xr(o,M,L,T,_o),t.quadraticCurveTo(go[1],_o[1],go[2],_o[2]);break t}g+=j}t.quadraticCurveTo(P,M,A,L),n=A,o=L;break;case vo.A:var I=p[x++],O=p[x++],R=p[x++],F=p[x++],B=p[x++],N=p[x++],H=p[x++],E=!p[x++],W=F<R?R:F,X=.001<Po(R-F),q=B+N,Y=!1;if(v&&(l<g+(j=h[_++])&&(q=B+N*(l-g)/j,Y=!0),g+=j),X&&t.ellipse?t.ellipse(I,O,R,F,H,B,q,E):t.arc(I,O,W,B,q,E),Y)break t;b&&(r=To(B)*R+I,i=Co(B)*F+O),n=To(q)*R+I,o=Co(q)*F+O;break;case vo.R:r=n=p[x],i=o=p[x+1],a=p[x++],s=p[x++];var j,V=p[x++],U=p[x++];if(v){if(l<g+(j=h[_++])){var G=l-g;t.moveTo(a,s),t.lineTo(a+So(G,V),s),0<(G-=V)&&t.lineTo(a+V,s+So(G,U)),0<(G-=U)&&t.lineTo(a+ko(V-G,0),s+U),0<(G-=V)&&t.lineTo(a,s+ko(U-G,0));break t}g+=j}t.rect(a,s,V,U);break;case vo.Z:if(v){if(l<g+(j=h[_++])){T=(l-g)/j,t.lineTo(n*(1-T)+r*T,o*(1-T)+i*T);break t}g+=j}t.closePath(),n=r,o=i}}},Ro.prototype.clone=function(){var t=new Ro,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},Ro.CMD=vo,Ro.initDefaultProps=((Io=Ro.prototype)._saveData=!0,Io._ux=0,Io._uy=0,Io._pendingPtDist=0,void(Io._version=0)),Ro);function Ro(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function Fo(t,e,r,i,n,o,a){if(0!==n){var s=n,h=0;if(!(e+s<a&&i+s<a||a<e-s&&a<i-s||t+s<o&&r+s<o||o<t-s&&o<r-s)){if(t===r)return Math.abs(o-t)<=s/2;var l=(h=(e-i)/(t-r))*o-a+(t*i-r*e)/(t-r);return l*l/(h*h+1)<=s/2*s/2}}}function Bo(t,e,r,i,n,o,a,s,h,l,u){if(0!==h){var c=h;if(!(e+c<u&&i+c<u&&o+c<u&&s+c<u||u<e-c&&u<i-c&&u<o-c&&u<s-c||t+c<l&&r+c<l&&n+c<l&&a+c<l||l<t-c&&l<r-c&&l<n-c&&l<a-c))return function(t,e,r,i,n,o,a,s,h,l,u){var c,p,f,d,y,v=.005,g=1/0;sr[0]=h,sr[1]=l;for(var _=0;_<1;_+=.05)hr[0]=pr(t,r,n,a,_),hr[1]=pr(e,i,o,s,_),(d=Pt(sr,hr))<g&&(c=_,g=d);g=1/0;for(var m=0;m<32&&!(v<nr);m++)p=c-v,f=c+v,hr[0]=pr(t,r,n,a,p),hr[1]=pr(e,i,o,s,p),d=Pt(hr,sr),0<=p&&d<g?(c=p,g=d):(lr[0]=pr(t,r,n,a,f),lr[1]=pr(e,i,o,s,f),y=Pt(lr,sr),f<=1&&y<g?(c=f,g=y):v*=.5);return u&&(u[0]=pr(t,r,n,a,c),u[1]=pr(e,i,o,s,c)),rr(g)}(t,e,r,i,n,o,a,s,l,u,null)<=c/2}}function No(t,e,r,i,n,o,a,s,h){if(0!==a){var l=a;if(!(e+l<h&&i+l<h&&o+l<h||h<e-l&&h<i-l&&h<o-l||t+l<s&&r+l<s&&n+l<s||s<t-l&&s<r-l&&s<n-l))return function(t,e,r,i,n,o,a,s,h){var l,u=.005,c=1/0;sr[0]=a,sr[1]=s;for(var p=0;p<1;p+=.05){hr[0]=gr(t,r,n,p),hr[1]=gr(e,i,o,p),(g=Pt(sr,hr))<c&&(l=p,c=g)}c=1/0;for(var f=0;f<32&&!(u<nr);f++){var d=l-u,y=l+u;hr[0]=gr(t,r,n,d),hr[1]=gr(e,i,o,d);var v,g=Pt(hr,sr);0<=d&&g<c?(l=d,c=g):(lr[0]=gr(t,r,n,y),lr[1]=gr(e,i,o,y),v=Pt(lr,sr),y<=1&&v<c?(l=y,c=v):u*=.5)}return h&&(h[0]=gr(t,r,n,l),h[1]=gr(e,i,o,l)),rr(c)}(t,e,r,i,n,o,s,h,null)<=l/2}}var Ho=2*Math.PI;function Eo(t){return(t%=Ho)<0&&(t+=Ho),t}var Wo=2*Math.PI;function Xo(t,e,r,i,n,o){if(e<o&&i<o||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!=a&&0!=a||(s=i<e?.5:-.5);var h=a*(r-t)+t;return h===n?1/0:n<h?s:0}var qo=Oo.CMD,Yo=2*Math.PI,jo=1e-4;var Vo=[-1,-1,-1],Uo=[-1,-1];function Go(t,e,r,i,n,o,a,s,h,l){if(e<l&&i<l&&o<l&&s<l||l<e&&l<i&&l<o&&l<s)return 0;var u=dr(e,i,o,s,l,Vo);if(0===u)return 0;for(var c,p=0,f=-1,d=void 0,y=void 0,v=0;v<u;v++){var g=Vo[v],_=0===g||1===g?.5:1;pr(t,r,n,a,g)<h||(f<0&&(f=yr(e,i,o,s,Uo),Uo[1]<Uo[0]&&1<f&&(c=void 0,c=Uo[0],Uo[0]=Uo[1],Uo[1]=c),d=pr(e,i,o,s,Uo[0]),1<f&&(y=pr(e,i,o,s,Uo[1]))),2===f?g<Uo[0]?p+=d<e?_:-_:g<Uo[1]?p+=y<d?_:-_:p+=s<y?_:-_:g<Uo[0]?p+=d<e?_:-_:p+=s<d?_:-_)}return p}function Zo(t,e,r,i,n,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;var h,l,u,c,p,f,d,y,v,g,_,m=(u=Vo,v=2*((l=i)-(h=e)),g=h-s,_=0,ur(y=h-2*l+o)?cr(v)&&0<=(f=-g/v)&&f<=1&&(u[_++]=f):ur(c=v*v-4*y*g)?0<=(f=-v/(2*y))&&f<=1&&(u[_++]=f):0<c&&(d=(-v-(p=rr(c)))/(2*y),0<=(f=(-v+p)/(2*y))&&f<=1&&(u[_++]=f),0<=d&&d<=1&&(u[_++]=d)),_);if(0===m)return 0;var x=mr(e,i,o);if(0<=x&&x<=1){for(var w=0,b=gr(e,i,o,x),S=0;S<m;S++){var k=0===Vo[S]||1===Vo[S]?.5:1;gr(t,r,n,Vo[S])<a||(Vo[S]<x?w+=b<e?k:-k:w+=o<b?k:-k)}return w}k=0===Vo[0]||1===Vo[0]?.5:1;return gr(t,r,n,Vo[0])<a?0:o<e?k:-k}function Ko(t,e,r,i,n){for(var o,a,s=t.data,h=t.len(),l=0,u=0,c=0,p=0,f=0,d=0;d<h;){var y=s[d++],v=1===d;switch(y===qo.M&&1<d&&(r||(l+=Xo(u,c,p,f,i,n))),v&&(p=u=s[d],f=c=s[d+1]),y){case qo.M:u=p=s[d++],c=f=s[d++];break;case qo.L:if(r){if(Fo(u,c,s[d],s[d+1],e,i,n))return!0}else l+=Xo(u,c,s[d],s[d+1],i,n)||0;u=s[d++],c=s[d++];break;case qo.C:if(r){if(Bo(u,c,s[d++],s[d++],s[d++],s[d++],s[d],s[d+1],e,i,n))return!0}else l+=Go(u,c,s[d++],s[d++],s[d++],s[d++],s[d],s[d+1],i,n)||0;u=s[d++],c=s[d++];break;case qo.Q:if(r){if(No(u,c,s[d++],s[d++],s[d],s[d+1],e,i,n))return!0}else l+=Zo(u,c,s[d++],s[d++],s[d],s[d+1],i,n)||0;u=s[d++],c=s[d++];break;case qo.A:var g=s[d++],_=s[d++],m=s[d++],x=s[d++],w=s[d++],b=s[d++];d+=1;var S=!!(1-s[d++]),k=Math.cos(w)*m+g,T=Math.sin(w)*x+_;v?(p=k,f=T):l+=Xo(u,c,k,T,i,n);var C=(i-g)*x/m+g;if(r){if(function(t,e,r,i,n,o,a,s,h){if(0!==a){var l=a;s-=t,h-=e;var u,c=Math.sqrt(s*s+h*h);if(!(r<c-l||c+l<r)){if(Math.abs(i-n)%Wo<1e-4)return 1;(n=o?(u=i,i=Eo(n),Eo(u)):(i=Eo(i),Eo(n)))<i&&(n+=Wo);var p=Math.atan2(h,s);return p<0&&(p+=Wo),i<=p&&p<=n||i<=p+Wo&&p+Wo<=n}}}(g,_,x,w,w+b,S,e,C,n))return!0}else l+=function(t,e,r,i,n,o,a,s){if(r<(s-=e)||s<-r)return 0;var h=Math.sqrt(r*r-s*s);Vo[0]=-h,Vo[1]=h;var l,u=Math.abs(i-n);if(u<1e-4)return 0;if(Yo-1e-4<=u){n=Yo;var c=o?1:-1;return a>=Vo[i=0]+t&&a<=Vo[1]+t?c:0}n<i&&(l=i,i=n,n=l),i<0&&(i+=Yo,n+=Yo);for(var p=0,f=0;f<2;f++){var d,y=Vo[f];a<y+t&&(c=o?1:-1,(d=Math.atan2(s,y))<0&&(d=Yo+d),(i<=d&&d<=n||i<=d+Yo&&d+Yo<=n)&&(d>Math.PI/2&&d<1.5*Math.PI&&(c=-c),p+=c))}return p}(g,_,x,w,w+b,S,C,n);u=Math.cos(w+b)*m+g,c=Math.sin(w+b)*x+_;break;case qo.R:p=u=s[d++],f=c=s[d++];if(k=p+s[d++],T=f+s[d++],r){if(Fo(p,f,k,f,e,i,n)||Fo(k,f,k,T,e,i,n)||Fo(k,T,p,T,e,i,n)||Fo(p,T,p,f,e,i,n))return!0}else l+=Xo(k,f,k,T,i,n),l+=Xo(p,T,p,f,i,n);break;case qo.Z:if(r){if(Fo(u,c,p,f,e,i,n))return!0}else l+=Xo(u,c,p,f,i,n);u=p,c=f}}return r||(o=c,a=f,Math.abs(o-a)<jo)||(l+=Xo(u,c,p,f,i,n)||0),0!==l}var Qo,$o,Jo=k({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},Un),ta={style:k({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},Gn.style)},ea=yn.concat(["invisible","culling","z","z2","zlevel","parent"]),ra=(yt(ia,Qo=Jn),ia.prototype.update=function(){var e=this;Qo.prototype.update.call(this);var t=this.style;if(t.decal){var r=this._decalEl=this._decalEl||new ia;r.buildPath===ia.prototype.buildPath&&(r.buildPath=function(t){e.buildPath(t,e.shape)}),r.silent=!0;var i=r.style;for(var n in t)i[n]!==t[n]&&(i[n]=t[n]);i.fill=t.fill?t.decal:null,i.decal=null,i.shadowColor=null,t.strokeFirst&&(i.stroke=null);for(var o=0;o<ea.length;++o)r[ea[o]]=this[ea[o]];r.__dirty|=Ve}else this._decalEl&&(this._decalEl=null)},ia.prototype.getDecalElement=function(){return this._decalEl},ia.prototype._init=function(t){var e=F(t);this.shape=this.getDefaultShape();var r=this.getDefaultStyle();r&&this.useStyle(r);for(var i=0;i<e.length;i++){var n=e[i],o=t[n];"style"===n?this.style?I(this.style,o):this.useStyle(o):"shape"===n?I(this.shape,o):Qo.prototype.attrKV.call(this,n,o)}this.style||this.useStyle({})},ia.prototype.getDefaultStyle=function(){return null},ia.prototype.getDefaultShape=function(){return{}},ia.prototype.canBeInsideText=function(){return this.hasFill()},ia.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if(N(t)){var e=Kr(t,0);return.5<e?nn:.2<e?"#eee":on}if(t)return on}return nn},ia.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(N(e)){var r=this.__zr;if(!(!r||!r.isDarkMode())==Kr(t,0)<.4)return e}},ia.prototype.buildPath=function(t,e,r){},ia.prototype.pathUpdated=function(){this.__dirty&=~Ue},ia.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},ia.prototype.createPathProxy=function(){this.path=new Oo(!1)},ia.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},ia.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},ia.prototype.getBoundingRect=function(){var t,e,r=this._rect,i=this.style,n=!r;if(n&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||this.__dirty&Ue)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),r=e.getBoundingRect()),this._rect=r,this.hasStroke()&&this.path&&0<this.path.len()){var o,a,s,h=this._rectStroke||(this._rectStroke=r.clone());return(this.__dirty||n)&&(h.copy(r),o=i.strokeNoScale?this.getLineScale():1,s=i.lineWidth,this.hasFill()||(a=this.strokeContainThreshold,s=Math.max(s,null==a?4:a)),1e-10<o&&(h.width+=s/o,h.height+=s/o,h.x-=s/o/2,h.y-=s/o/2)),h}return r},ia.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),n=this.style;if(t=r[0],e=r[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=n.lineWidth,s=n.strokeNoScale?this.getLineScale():1;if(1e-10<s&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),Ko(o,a/s,!0,t,e)))return!0}if(this.hasFill())return Ko(o,0,!1,t,e)}return!1},ia.prototype.dirtyShape=function(){this.__dirty|=Ue,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},ia.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},ia.prototype.animateShape=function(t){return this.animate("shape",t)},ia.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},ia.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):Qo.prototype.attrKV.call(this,t,e)},ia.prototype.setShape=function(t,e){var r=(r=this.shape)||(this.shape={});return"string"==typeof t?r[t]=e:I(r,t),this.dirtyShape(),this},ia.prototype.shapeChanged=function(){return!!(this.__dirty&Ue)},ia.prototype.createStyle=function(t){return ht(Jo,t)},ia.prototype._innerSaveToNormal=function(t){Qo.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=I({},this.shape))},ia.prototype._applyStateObj=function(t,e,r,i,n,o){Qo.prototype._applyStateObj.call(this,t,e,r,i,n,o);var a,s=!(e&&i);if(e&&e.shape?n?i?a=e.shape:(a=I({},r.shape),I(a,e.shape)):(a=I({},i?this.shape:r.shape),I(a,e.shape)):s&&(a=r.shape),a)if(n){this.shape=I({},this.shape);for(var h={},l=F(a),u=0;u<l.length;u++){var c=l[u];"object"==typeof a[c]?this.shape[c]=a[c]:h[c]=a[c]}this._transitionState(t,{shape:h},o)}else this.shape=a,this.dirtyShape()},ia.prototype._mergeStates=function(t){for(var e,r=Qo.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var n=t[i];n.shape&&(e=e||{},this._mergeStyle(e,n.shape))}return e&&(r.shape=e),r},ia.prototype.getAnimationStyleProps=function(){return ta},ia.prototype.isZeroArea=function(){return!1},ia.extend=function(r){var i,t=(yt(e,i=ia),e.prototype.getDefaultStyle=function(){return C(r.style)},e.prototype.getDefaultShape=function(){return C(r.shape)},e);function e(t){var e=i.call(this,t)||this;return r.init&&r.init.call(e,t),e}for(var n in r)"function"==typeof r[n]&&(t.prototype[n]=r[n]);return t},ia.initDefaultProps=(($o=ia.prototype).type="path",$o.strokeContainThreshold=5,$o.segmentIgnoreThreshold=0,$o.subPixelOptimize=!1,$o.autoBatch=!1,void($o.__dirty=2|Ve|Ue)),ia);function ia(t){return Qo.call(this,t)||this}var na=Oo.CMD,oa=[[],[],[]],aa=Math.sqrt,sa=Math.atan2;function ha(t,e){if(e){for(var r,i,n,o,a=t.data,s=t.len(),h=na.M,l=na.C,u=na.L,c=na.R,p=na.A,f=na.Q,d=0,y=0;d<s;){switch(r=a[d++],y=d,i=0,r){case h:case u:i=1;break;case l:i=3;break;case f:i=2;break;case p:var v=e[4],g=e[5],_=aa(e[0]*e[0]+e[1]*e[1]),m=aa(e[2]*e[2]+e[3]*e[3]),x=sa(-e[1]/m,e[0]/_);a[d]*=_,a[d++]+=v,a[d]*=m,a[d++]+=g,a[d++]*=_,a[d++]*=m,a[d++]+=x,a[d++]+=x,y=d+=2;break;case c:o[0]=a[d++],o[1]=a[d++],At(o,o,e),a[y++]=o[0],a[y++]=o[1],o[0]+=a[d++],o[1]+=a[d++],At(o,o,e),a[y++]=o[0],a[y++]=o[1]}for(n=0;n<i;n++){var w=oa[n];w[0]=a[d++],w[1]=a[d++],At(w,w,e),a[y++]=w[0],a[y++]=w[1]}}t.increaseVersion()}}var la=Math.sqrt,ua=Math.sin,ca=Math.cos,pa=Math.PI;function fa(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function da(t,e){return(t[0]*e[0]+t[1]*e[1])/(fa(t)*fa(e))}function ya(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(da(t,e))}function va(t,e,r,i,n,o,a,s,h,l,u){var c=h*(pa/180),p=ca(c)*(t-r)/2+ua(c)*(e-i)/2,f=-1*ua(c)*(t-r)/2+ca(c)*(e-i)/2,d=p*p/(a*a)+f*f/(s*s);1<d&&(a*=la(d),s*=la(d));var y,v=(n===o?-1:1)*la((a*a*(s*s)-a*a*(f*f)-s*s*(p*p))/(a*a*(f*f)+s*s*(p*p)))||0,g=v*a*f/s,_=v*-s*p/a,m=(t+r)/2+ca(c)*g-ua(c)*_,x=(e+i)/2+ua(c)*g+ca(c)*_,w=ya([1,0],[(p-g)/a,(f-_)/s]),b=[(p-g)/a,(f-_)/s],S=[(-1*p-g)/a,(-1*f-_)/s],k=ya(b,S);da(b,S)<=-1&&(k=pa),1<=da(b,S)&&(k=0),k<0&&(y=Math.round(k/pa*1e6)/1e6,k=2*pa+y%2*pa),u.addData(l,m,x,a,s,w,k,c,o)}var ga=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,_a=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;var ma,xa=(yt(wa,ma=ra),wa.prototype.applyTransform=function(t){},wa);function wa(){return null!==ma&&ma.apply(this,arguments)||this}function ba(t){return null!=t.setData}function Sa(t,e){var r=function(t){var e=new Oo;if(!t)return e;var r,i=0,n=0,o=i,a=n,s=Oo.CMD,h=t.match(ga);if(!h)return e;for(var l=0;l<h.length;l++){for(var u=h[l],c=u.charAt(0),p=void 0,f=u.match(_a)||[],d=f.length,y=0;y<d;y++)f[y]=parseFloat(f[y]);for(var v=0;v<d;){var g=void 0,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0,S=void 0,k=i,T=n,C=void 0,P=void 0;switch(c){case"l":i+=f[v++],n+=f[v++],p=s.L,e.addData(p,i,n);break;case"L":i=f[v++],n=f[v++],p=s.L,e.addData(p,i,n);break;case"m":i+=f[v++],n+=f[v++],p=s.M,e.addData(p,i,n),o=i,a=n,c="l";break;case"M":i=f[v++],n=f[v++],p=s.M,e.addData(p,i,n),o=i,a=n,c="L";break;case"h":i+=f[v++],p=s.L,e.addData(p,i,n);break;case"H":i=f[v++],p=s.L,e.addData(p,i,n);break;case"v":n+=f[v++],p=s.L,e.addData(p,i,n);break;case"V":n=f[v++],p=s.L,e.addData(p,i,n);break;case"C":p=s.C,e.addData(p,f[v++],f[v++],f[v++],f[v++],f[v++],f[v++]),i=f[v-2],n=f[v-1];break;case"c":p=s.C,e.addData(p,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n),i+=f[v-2],n+=f[v-1];break;case"S":g=i,_=n,C=e.len(),P=e.data,r===s.C&&(g+=i-P[C-4],_+=n-P[C-3]),p=s.C,k=f[v++],T=f[v++],i=f[v++],n=f[v++],e.addData(p,g,_,k,T,i,n);break;case"s":g=i,_=n,C=e.len(),P=e.data,r===s.C&&(g+=i-P[C-4],_+=n-P[C-3]),p=s.C,k=i+f[v++],T=n+f[v++],i+=f[v++],n+=f[v++],e.addData(p,g,_,k,T,i,n);break;case"Q":k=f[v++],T=f[v++],i=f[v++],n=f[v++],p=s.Q,e.addData(p,k,T,i,n);break;case"q":k=f[v++]+i,T=f[v++]+n,i+=f[v++],n+=f[v++],p=s.Q,e.addData(p,k,T,i,n);break;case"T":g=i,_=n,C=e.len(),P=e.data,r===s.Q&&(g+=i-P[C-4],_+=n-P[C-3]),i=f[v++],n=f[v++],p=s.Q,e.addData(p,g,_,i,n);break;case"t":g=i,_=n,C=e.len(),P=e.data,r===s.Q&&(g+=i-P[C-4],_+=n-P[C-3]),i+=f[v++],n+=f[v++],p=s.Q,e.addData(p,g,_,i,n);break;case"A":m=f[v++],x=f[v++],w=f[v++],b=f[v++],S=f[v++],va(k=i,T=n,i=f[v++],n=f[v++],b,S,m,x,w,p=s.A,e);break;case"a":m=f[v++],x=f[v++],w=f[v++],b=f[v++],S=f[v++],va(k=i,T=n,i+=f[v++],n+=f[v++],b,S,m,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,n=a),r=p}return e.toStatic(),e}(t),i=I({},e);return i.buildPath=function(t){var e;ba(t)?(t.setData(r.data),(e=t.getContext())&&t.rebuildPath(e,1)):(e=t,r.rebuildPath(e,1))},i.applyTransform=function(t){ha(r,t),this.dirtyShape()},i}function ka(t,e){return new xa(Sa(t,e))}function Ta(t,e){e=e||{};var r=new ra;return t.shape&&r.setShape(t.shape),r.setStyle(t.style),e.bakeTransform?ha(r.path,t.getComputedTransform()):e.toLocal?r.setLocalTransform(t.getComputedTransform()):r.copyTransform(t),r.buildPath=t.buildPath,r.applyTransform=r.applyTransform,r.z=t.z,r.z2=t.z2,r.zlevel=t.zlevel,r}var Ca=Object.freeze({__proto__:null,createFromString:ka,extendFromString:function(t,e){var r,i=Sa(t,e);function n(t){var e=r.call(this,t)||this;return e.applyTransform=i.applyTransform,e.buildPath=i.buildPath,e}return yt(n,r=xa),n},mergePath:function(t,e){for(var r=[],i=t.length,n=0;n<i;n++){var o=t[n];r.push(o.getUpdatedPathProxy(!0))}var a=new ra(e);return a.createPathProxy(),a.buildPath=function(t){var e;ba(t)&&(t.appendPath(r),(e=t.getContext())&&t.rebuildPath(e,1))},a},clonePath:Ta}),Pa=k({x:0,y:0},Un),Ma={style:k({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},Gn.style)};var Aa,La=(yt(Da,Aa=Jn),Da.prototype.createStyle=function(t){return ht(Pa,t)},Da.prototype._getSize=function(t){var e=this.style,r=e[t];if(null!=r)return r;var i,n=(i=e.image)&&"string"!=typeof i&&i.width&&i.height?e.image:this.__image;if(!n)return 0;var o="width"===t?"height":"width",a=e[o];return null==a?n[t]:n[t]/n[o]*a},Da.prototype.getWidth=function(){return this._getSize("width")},Da.prototype.getHeight=function(){return this._getSize("height")},Da.prototype.getAnimationStyleProps=function(){return Ma},Da.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new Se(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},Da);function Da(){return null!==Aa&&Aa.apply(this,arguments)||this}La.prototype.type="image";var za,Ia=function(){this.cx=0,this.cy=0,this.r=0},Oa=(yt(Ra,za=ra),Ra.prototype.getDefaultShape=function(){return new Ia},Ra.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},Ra);function Ra(t){return za.call(this,t)||this}Oa.prototype.type="circle";var Fa=Math.round;function Ba(t,e,r){if(!e)return t;var i=Fa(2*t);return(i+Fa(e))%2==0?i/2:(i+(r?1:-1))/2}var Na,Ha=function(){this.x=0,this.y=0,this.width=0,this.height=0},Ea={},Wa=(yt(Xa,Na=ra),Xa.prototype.getDefaultShape=function(){return new Ha},Xa.prototype.buildPath=function(t,e){var r,i,n,o,a,s,h,l,u,c,p,f,d,y,v,g,_;this.subPixelOptimize?(i=(r=function(t,e,r){if(e){var i=e.x,n=e.y,o=e.width,a=e.height;t.x=i,t.y=n,t.width=o,t.height=a;var s=r&&r.lineWidth;return s&&(t.x=Ba(i,s,!0),t.y=Ba(n,s,!0),t.width=Math.max(Ba(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(Ba(n+a,s,!1)-t.y,0===a?0:1)),t}}(Ea,e,this.style)).x,n=r.y,o=r.width,a=r.height,r.r=e.r,e=r):(i=e.x,n=e.y,o=e.width,a=e.height),e.r?(s=t,d=(h=e).x,y=h.y,v=h.width,g=h.height,_=h.r,v<0&&(d+=v,v=-v),g<0&&(y+=g,g=-g),"number"==typeof _?l=u=c=p=_:_ instanceof Array?1===_.length?l=u=c=p=_[0]:2===_.length?(l=c=_[0],u=p=_[1]):3===_.length?(l=_[0],u=p=_[1],c=_[2]):(l=_[0],u=_[1],c=_[2],p=_[3]):l=u=c=p=0,v<l+u&&(l*=v/(f=l+u),u*=v/f),v<c+p&&(c*=v/(f=c+p),p*=v/f),g<u+c&&(u*=g/(f=u+c),c*=g/f),g<l+p&&(l*=g/(f=l+p),p*=g/f),s.moveTo(d+l,y),s.lineTo(d+v-u,y),0!==u&&s.arc(d+v-u,y+u,u,-Math.PI/2,0),s.lineTo(d+v,y+g-c),0!==c&&s.arc(d+v-c,y+g-c,c,0,Math.PI/2),s.lineTo(d+p,y+g),0!==p&&s.arc(d+p,y+g-p,p,Math.PI/2,Math.PI),s.lineTo(d,y+l),0!==l&&s.arc(d+l,y+l,l,Math.PI,1.5*Math.PI)):t.rect(i,n,o,a)},Xa.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},Xa);function Xa(t){return Na.call(this,t)||this}Wa.prototype.type="rect";var qa,Ya=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},ja=(yt(Va,qa=ra),Va.prototype.getDefaultShape=function(){return new Ya},Va.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=e.rx,o=e.ry,a=.5522848*n,s=.5522848*o;t.moveTo(r-n,i),t.bezierCurveTo(r-n,i-s,r-a,i-o,r,i-o),t.bezierCurveTo(r+a,i-o,r+n,i-s,r+n,i),t.bezierCurveTo(r+n,i+s,r+a,i+o,r,i+o),t.bezierCurveTo(r-a,i+o,r-n,i+s,r-n,i),t.closePath()},Va);function Va(t){return qa.call(this,t)||this}ja.prototype.type="ellipse";var Ua,Ga={},Za=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},Ka=(yt(Qa,Ua=ra),Qa.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Qa.prototype.getDefaultShape=function(){return new Za},Qa.prototype.buildPath=function(t,e){var r,i,n,o,a;a=this.subPixelOptimize?(i=(r=function(t,e,r){if(e){var i=e.x1,n=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=n,t.y1=o,t.y2=a;var s=r&&r.lineWidth;return s&&(Fa(2*i)===Fa(2*n)&&(t.x1=t.x2=Ba(i,s,!0)),Fa(2*o)===Fa(2*a)&&(t.y1=t.y2=Ba(o,s,!0))),t}}(Ga,e,this.style)).x1,n=r.y1,o=r.x2,r.y2):(i=e.x1,n=e.y1,o=e.x2,e.y2);var s=e.percent;0!==s&&(t.moveTo(i,n),s<1&&(o=i*(1-s)+o*s,a=n*(1-s)+a*s),t.lineTo(o,a))},Qa.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},Qa);function Qa(t){return Ua.call(this,t)||this}function $a(t,e,r){var i=e.smooth,n=e.points;if(n&&2<=n.length){if(i){var o=function(t,e,r,i){var n,o,a,s,h=[],l=[],u=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var p=0,f=t.length;p<f;p++)Lt(a,a,t[p]),Dt(s,s,t[p]);Lt(a,a,i[0]),Dt(s,s,i[1])}for(p=0,f=t.length;p<f;p++){var d=t[p];if(r)n=t[p?p-1:f-1],o=t[(p+1)%f];else{if(0===p||p===f-1){h.push(gt(t[p]));continue}n=t[p-1],o=t[p+1]}mt(l,o,n),bt(l,l,e);var y=kt(d,n),v=kt(d,o),g=y+v;0!==g&&(y/=g,v/=g),bt(u,l,-y),bt(c,l,v);var _=_t([],d,u),m=_t([],d,c);i&&(Dt(_,_,a),Lt(_,_,s),Dt(m,m,a),Lt(m,m,s)),h.push(_),h.push(m)}return r&&h.push(h.shift()),h}(n,i,r,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var a=n.length,s=0;s<(r?a:a-1);s++){var h=o[2*s],l=o[2*s+1],u=n[(s+1)%a];t.bezierCurveTo(h[0],h[1],l[0],l[1],u[0],u[1])}}else{t.moveTo(n[0][0],n[0][1]);for(var s=1,c=n.length;s<c;s++)t.lineTo(n[s][0],n[s][1])}r&&t.closePath()}}Ka.prototype.type="line";var Ja,ts=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},es=(yt(rs,Ja=ra),rs.prototype.getDefaultShape=function(){return new ts},rs.prototype.buildPath=function(t,e){$a(t,e,!0)},rs);function rs(t){return Ja.call(this,t)||this}es.prototype.type="polygon";var is,ns=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},os=(yt(as,is=ra),as.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},as.prototype.getDefaultShape=function(){return new ns},as.prototype.buildPath=function(t,e){$a(t,e,!1)},as);function as(t){return is.call(this,t)||this}os.prototype.type="polyline";var ss=(hs.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},hs);function hs(t){this.colorStops=t||[]}var ls,us=(yt(cs,ls=ss),cs);function cs(t,e,r,i,n,o){var a=ls.call(this,n)||this;return a.x=null==t?0:t,a.y=null==e?0:e,a.x2=null==r?1:r,a.y2=null==i?0:i,a.type="linear",a.global=o||!1,a}var ps,fs=(yt(ds,ps=ss),ds);function ds(t,e,r,i,n){var o=ps.call(this,i)||this;return o.x=null==t?.5:t,o.y=null==e?.5:e,o.r=null==r?.5:r,o.type="radial",o.global=n||!1,o}var ys,vs,gs=k({strokeFirst:!0,font:E,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Jo),_s=(yt(ms,ys=Jn),ms.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth},ms.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},ms.prototype.createStyle=function(t){return ht(gs,t)},ms.prototype.setBoundingRect=function(t){this._rect=t},ms.prototype.getBoundingRect=function(){var t,e,r,i=this.style;return this._rect||(null!=(t=i.text)?t+="":t="",(e=function(t,e,r,i){var n=((t||"")+"").split("\n");if(1===n.length)return mn(n[0],e,r,i);for(var o=new Se(0,0,0,0),a=0;a<n.length;a++){var s=mn(n[a],e,r,i);0===a?o.copy(s):o.union(s)}return o}(t,i.font,i.textAlign,i.textBaseline)).x+=i.x||0,e.y+=i.y||0,this.hasStroke()&&(r=i.lineWidth,e.x-=r/2,e.y-=r/2,e.width+=r,e.height+=r),this._rect=e),this._rect},ms.initDefaultProps=void(ms.prototype.dirtyRectTolerance=10),ms);function ms(){return null!==ys&&ys.apply(this,arguments)||this}_s.prototype.type="tspan";var xs={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},ws=F(xs),bs={"alignment-baseline":"textBaseline","stop-color":"stopColor"},Ss=F(bs),ks=(Ts.prototype.parse=function(t,e){e=e||{};var r=function(t){N(t)&&(t=(new DOMParser).parseFromString(t,"text/xml"));var e=t;for(9===e.nodeType&&(e=e.firstChild);"svg"!==e.nodeName.toLowerCase()||1!==e.nodeType;)e=e.nextSibling;return e}(t);this._defsUsePending=[];var i=new Bn;this._root=i;var n=[],o=r.getAttribute("viewBox")||"",a=parseFloat(r.getAttribute("width")||e.width),s=parseFloat(r.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(s)&&(s=null),Ds(r,i,null,!0,!1);for(var h,l,u,c,p,f,d,y,v,g=r.firstChild;g;)this._parseNode(g,i,n,null,!1,!1),g=g.nextSibling;return function(t,e){for(var r=0;r<e.length;r++){var i=e[r];i[0].style[i[1]]=t[i[2]]}}(this._defs,this._defsUsePending),this._defsUsePending=[],!o||4<=(u=Rs(o)).length&&(h={x:parseFloat(u[0]||0),y:parseFloat(u[1]||0),width:parseFloat(u[2]),height:parseFloat(u[3])}),h&&null!=a&&null!=s&&(d=(f={x:0,y:0,width:a,height:s}).width/(p=h).width,y=f.height/p.height,l={scale:v=Math.min(d,y),x:-(p.x+p.width/2)*v+(f.x+f.width/2),y:-(p.y+p.height/2)*v+(f.y+f.height/2)},e.ignoreViewBox||(c=i,(i=new Bn).add(c),c.scaleX=c.scaleY=l.scale,c.x=l.x,c.y=l.y)),e.ignoreRootClip||null==a||null==s||i.setClipPath(new Wa({shape:{x:0,y:0,width:a,height:s}})),{root:i,width:a,height:s,viewBoxRect:h,viewBoxTransform:l,named:n}},Ts.prototype._parseNode=function(t,e,r,i,n,o){var a,s,h,l,u,c,p,f=t.nodeName.toLowerCase(),d=i;if("defs"===f&&(n=!0),"text"===f&&(o=!0),"defs"===f||"switch"===f?a=e:(n||(s=vs[f])&&ut(vs,f)&&(a=s.call(this,t,e),(h=t.getAttribute("name"))?(l={name:h,namedFrom:null,svgNodeTagLower:f,el:a},r.push(l),"g"===f&&(d=l)):i&&r.push({name:i.name,namedFrom:i,svgNodeTagLower:f,el:a}),e.add(a)),(u=Cs[f])&&ut(Cs,f)&&(c=u.call(this,t),(p=t.getAttribute("id"))&&(this._defs[p]=c))),a&&a.isGroup)for(var y=t.firstChild;y;)1===y.nodeType?this._parseNode(y,a,r,d,n,o):3===y.nodeType&&o&&this._parseText(y,a),y=y.nextSibling},Ts.prototype._parseText=function(t,e){var r=new _s({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});As(e,r),Ds(t,r,this._defsUsePending,!1,!1),function(t,e){var r,i,n=e.__selfStyle;n&&(r=n.textBaseline,(i=r)&&"auto"!==r&&"baseline"!==r?"before-edge"===r||"text-before-edge"===r?i="top":"after-edge"===r||"text-after-edge"===r?i="bottom":"central"!==r&&"mathematical"!==r||(i="middle"):i="alphabetic",t.style.textBaseline=i);var o,a,s=e.__inheritedStyle;s&&(o=s.textAlign,(a=o)&&("middle"===o&&(a="center"),t.style.textAlign=a))}(r,e);var i=r.style,n=i.fontSize;n&&n<9&&(i.fontSize=9,r.scaleX*=n/9,r.scaleY*=n/9);var o=(i.fontSize||i.fontFamily)&&[i.fontStyle,i.fontWeight,(i.fontSize||12)+"px",i.fontFamily||"sans-serif"].join(" ");i.font=o;var a=r.getBoundingRect();return this._textX+=a.width,e.add(r),r},Ts.internalField=void(vs={g:function(t,e){var r=new Bn;return As(e,r),Ds(t,r,this._defsUsePending,!1,!1),r},rect:function(t,e){var r=new Wa;return As(e,r),Ds(t,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(t,e){var r=new Oa;return As(e,r),Ds(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),r.silent=!0,r},line:function(t,e){var r=new Ka;return As(e,r),Ds(t,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(t,e){var r=new ja;return As(e,r),Ds(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(t,e){var r,i=t.getAttribute("points");i&&(r=Ls(i));var n=new es({shape:{points:r||[]},silent:!0});return As(e,n),Ds(t,n,this._defsUsePending,!1,!1),n},polyline:function(t,e){var r,i=t.getAttribute("points");i&&(r=Ls(i));var n=new os({shape:{points:r||[]},silent:!0});return As(e,n),Ds(t,n,this._defsUsePending,!1,!1),n},image:function(t,e){var r=new La;return As(e,r),Ds(t,r,this._defsUsePending,!1,!1),r.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),r.silent=!0,r},text:function(t,e){var r=t.getAttribute("x")||"0",i=t.getAttribute("y")||"0",n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(n),this._textY=parseFloat(i)+parseFloat(o);var a=new Bn;return As(e,a),Ds(t,a,this._defsUsePending,!1,!0),a},tspan:function(t,e){var r=t.getAttribute("x"),i=t.getAttribute("y");null!=r&&(this._textX=parseFloat(r)),null!=i&&(this._textY=parseFloat(i));var n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0",a=new Bn;return As(e,a),Ds(t,a,this._defsUsePending,!1,!0),this._textX+=parseFloat(n),this._textY+=parseFloat(o),a},path:function(t,e){var r=ka(t.getAttribute("d")||"");return As(e,r),Ds(t,r,this._defsUsePending,!1,!1),r.silent=!0,r}}),Ts);function Ts(){this._defs={},this._root=null}var Cs={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||"0",10),r=parseInt(t.getAttribute("y1")||"0",10),i=parseInt(t.getAttribute("x2")||"10",10),n=parseInt(t.getAttribute("y2")||"0",10),o=new us(e,r,i,n);return Ps(t,o),Ms(t,o),o},radialgradient:function(t){var e=parseInt(t.getAttribute("cx")||"0",10),r=parseInt(t.getAttribute("cy")||"0",10),i=parseInt(t.getAttribute("r")||"0",10),n=new fs(e,r,i);return Ps(t,n),Ms(t,n),n}};function Ps(t,e){"userSpaceOnUse"===t.getAttribute("gradientUnits")&&(e.global=!0)}function Ms(t,e){for(var r,i,n,o,a=t.firstChild;a;){1===a.nodeType&&"stop"===a.nodeName.toLocaleLowerCase()&&(i=void 0,i=(r=a.getAttribute("offset"))&&0<r.indexOf("%")?parseInt(r,10)/100:r?parseFloat(r):0,Hs(a,n={},n),o=n.stopColor||a.getAttribute("stop-color")||"#000000",e.colorStops.push({offset:i,color:o})),a=a.nextSibling}}function As(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),k(e.__inheritedStyle,t.__inheritedStyle))}function Ls(t){for(var e=Rs(t),r=[],i=0;i<e.length;i+=2){var n=parseFloat(e[i]),o=parseFloat(e[i+1]);r.push([n,o])}return r}function Ds(t,e,r,i,n){var o=e,a=o.__inheritedStyle=o.__inheritedStyle||{},s={};1===t.nodeType&&(function(t,e){var r=t.getAttribute("transform");if(r){r=r.replace(/,/g," ");var i=[],n=null;r.replace(Fs,function(t,e,r){return i.push(e,r),""});for(var o=i.length-1;0<o;o-=2){var a=i[o],s=i[o-1],h=Rs(a);switch(n=n||ne(),s){case"translate":he(n,n,[parseFloat(h[0]),parseFloat(h[1]||"0")]);break;case"scale":ue(n,n,[parseFloat(h[0]),parseFloat(h[1]||h[0])]);break;case"rotate":le(n,n,-parseFloat(h[0])*Bs,[parseFloat(h[1]||"0"),parseFloat(h[2]||"0")]);break;case"skewX":var l=Math.tan(parseFloat(h[0])*Bs);se(n,[1,0,l,1,0,0],n);break;case"skewY":var u=Math.tan(parseFloat(h[0])*Bs);se(n,[1,u,0,1,0,0],n);break;case"matrix":n[0]=parseFloat(h[0]),n[1]=parseFloat(h[1]),n[2]=parseFloat(h[2]),n[3]=parseFloat(h[3]),n[4]=parseFloat(h[4]),n[5]=parseFloat(h[5])}}e.setLocalTransform(n)}}(t,e),Hs(t,a,s),i||function(t,e,r){for(var i=0;i<ws.length;i++){var n=ws[i];null!=(o=t.getAttribute(n))&&(e[xs[n]]=o)}for(i=0;i<Ss.length;i++){var o,n=Ss[i];null!=(o=t.getAttribute(n))&&(r[bs[n]]=o)}}(t,a,s)),o.style=o.style||{},null!=a.fill&&(o.style.fill=Is(o,"fill",a.fill,r)),null!=a.stroke&&(o.style.stroke=Is(o,"stroke",a.stroke,r)),O(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){null!=a[t]&&(o.style[t]=parseFloat(a[t]))}),O(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(t){null!=a[t]&&(o.style[t]=a[t])}),n&&(o.__selfStyle=s),a.lineDash&&(o.style.lineDash=L(Rs(a.lineDash),function(t){return parseFloat(t)})),"hidden"!==a.visibility&&"collapse"!==a.visibility||(o.invisible=!0),"none"===a.display&&(o.ignore=!0)}var zs=/^url\(\s*#(.*?)\)/;function Is(t,e,r,i){var n=r&&r.match(zs);if(!n)return"none"===r&&(r=null),r;var o=tt(n[1]);i.push([t,e,o])}var Os=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Rs(t){return t.match(Os)||[]}var Fs=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Bs=Math.PI/180;var Ns=/([^\s:;]+)\s*:\s*([^:;]+)/g;function Hs(t,e,r){var i,n=t.getAttribute("style");if(n)for(Ns.lastIndex=0;null!=(i=Ns.exec(n));){var o=i[1],a=ut(xs,o)?xs[o]:null;a&&(e[a]=i[2]);var s=ut(bs,o)?bs[o]:null;s&&(r[s]=i[2])}}var Es=Math.PI,Ws=2*Es,Xs=Math.sin,qs=Math.cos,Ys=Math.acos,js=Math.atan2,Vs=Math.abs,Us=Math.sqrt,Gs=Math.max,Zs=Math.min,Ks=1e-4;function Qs(t,e,r,i,n,o,a){var s=t-r,h=e-i,l=(a?o:-o)/Us(s*s+h*h),u=l*h,c=-l*s,p=t+u,f=e+c,d=r+u,y=i+c,v=(p+d)/2,g=(f+y)/2,_=d-p,m=y-f,x=_*_+m*m,w=n-o,b=p*y-d*f,S=(m<0?-1:1)*Us(Gs(0,w*w*x-b*b)),k=(b*m-_*S)/x,T=(-b*_-m*S)/x,C=(b*m+_*S)/x,P=(-b*_+m*S)/x,M=k-v,A=T-g,L=C-v,D=P-g;return L*L+D*D<M*M+A*A&&(k=C,T=P),{cx:k,cy:T,x0:-u,y0:-c,x1:k*(n/w-1),y1:T*(n/w-1)}}function $s(t,e){var r,i,n,o,a,s,h,l,u,c,p,f,d,y,v,g,_,m,x,w,b,S,k,T,C,P,M,A,L,D,z,I,O,R,F,B,N,H,E,W,X,q,Y,j=Gs(e.r,0),V=Gs(e.r0||0,0),U=0<j;(U||0<V)&&(U||(j=V,V=0),j<V&&(i=j,j=V,V=i),n=e.startAngle,o=e.endAngle,isNaN(n)||isNaN(o)||(a=e.cx,s=e.cy,h=!!e.clockwise,l=Vs(o-n),Ks<(u=Ws<l&&l%Ws)&&(l=u),Ks<j?Ws-Ks<l?(t.moveTo(a+j*qs(n),s+j*Xs(n)),t.arc(a,s,j,n,o,!h),Ks<V&&(t.moveTo(a+V*qs(o),s+V*Xs(o)),t.arc(a,s,V,o,n,h))):(F=R=O=I=z=D=v=y=E=H=N=B=d=f=p=c=void 0,g=j*qs(n),_=j*Xs(n),m=V*qs(o),x=V*Xs(o),(w=Ks<l)&&((b=e.cornerRadius)&&(c=(r=function(t){var e;if(G(t)){var r=t.length;if(!r)return t;e=1===r?[t[0],t[0],0,0]:2===r?[t[0],t[0],t[1],t[1]]:3===r?t.concat(t[2]):t}else e=[t,t,t,t];return e}(b))[0],p=r[1],f=r[2],d=r[3]),S=Vs(j-V)/2,B=Zs(S,f),N=Zs(S,d),H=Zs(S,c),E=Zs(S,p),D=y=Gs(B,N),z=v=Gs(H,E),(Ks<y||Ks<v)&&(I=j*qs(o),O=j*Xs(o),R=V*qs(n),F=V*Xs(n),l<Es&&((k=function(t,e,r,i,n,o,a,s){var h=r-t,l=i-e,u=a-n,c=s-o,p=c*h-u*l;if(!(p*p<Ks))return[t+(p=(u*(e-o)-c*(t-n))/p)*h,e+p*l]}(g,_,R,F,I,O,m,x))&&(T=g-k[0],C=_-k[1],P=I-k[0],M=O-k[1],A=1/Xs(Ys((T*P+C*M)/(Us(T*T+C*C)*Us(P*P+M*M)))/2),L=Us(k[0]*k[0]+k[1]*k[1]),D=Zs(y,(j-L)/(1+A)),z=Zs(v,(V-L)/(A-1)))))),w?Ks<D?(W=Zs(f,D),X=Zs(d,D),q=Qs(R,F,g,_,j,W,h),Y=Qs(I,O,m,x,j,X,h),t.moveTo(a+q.cx+q.x0,s+q.cy+q.y0),D<y&&W===X?t.arc(a+q.cx,s+q.cy,D,js(q.y0,q.x0),js(Y.y0,Y.x0),!h):(0<W&&t.arc(a+q.cx,s+q.cy,W,js(q.y0,q.x0),js(q.y1,q.x1),!h),t.arc(a,s,j,js(q.cy+q.y1,q.cx+q.x1),js(Y.cy+Y.y1,Y.cx+Y.x1),!h),0<X&&t.arc(a+Y.cx,s+Y.cy,X,js(Y.y1,Y.x1),js(Y.y0,Y.x0),!h))):(t.moveTo(a+g,s+_),t.arc(a,s,j,n,o,!h)):t.moveTo(a+g,s+_),Ks<V&&w?Ks<z?(W=Zs(c,z),q=Qs(m,x,I,O,V,-(X=Zs(p,z)),h),Y=Qs(g,_,R,F,V,-W,h),t.lineTo(a+q.cx+q.x0,s+q.cy+q.y0),z<v&&W===X?t.arc(a+q.cx,s+q.cy,z,js(q.y0,q.x0),js(Y.y0,Y.x0),!h):(0<X&&t.arc(a+q.cx,s+q.cy,X,js(q.y0,q.x0),js(q.y1,q.x1),!h),t.arc(a,s,V,js(q.cy+q.y1,q.cx+q.x1),js(Y.cy+Y.y1,Y.cx+Y.x1),h),0<W&&t.arc(a+Y.cx,s+Y.cy,W,js(Y.y1,Y.x1),js(Y.y0,Y.x0),!h))):(t.lineTo(a+m,s+x),t.arc(a,s,V,o,n,h)):t.lineTo(a+m,s+x)):t.moveTo(a,s),t.closePath()))}var Js,th=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},eh=(yt(rh,Js=ra),rh.prototype.getDefaultShape=function(){return new th},rh.prototype.buildPath=function(t,e){$s(t,e)},rh.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},rh);function rh(t){return Js.call(this,t)||this}eh.prototype.type="sector";var ih=Oo.CMD;function nh(t,e){return Math.abs(t-e)<1e-5}function oh(t){var n,e,r,i=t.data,o=t.len(),a=[],s=0,h=0,l=0,u=0;function c(t,e){n&&2<n.length&&a.push(n),n=[t,e]}function p(t,e,r,i){nh(t,r)&&nh(e,i)||n.push(t,e,r,i,r,i)}for(var f,d,y,v,g,_,m,x,w,b,S,k,T,C,P,M,A,L,D,z=0;z<o;){var I=i[z++],O=1===z;switch(O&&(l=s=i[z],u=h=i[z+1],I!==ih.L&&I!==ih.C&&I!==ih.Q||(n=[l,u])),I){case ih.M:s=l=i[z++],h=u=i[z++],c(l,u);break;case ih.L:p(s,h,X=i[z++],q=i[z++]),s=X,h=q;break;case ih.C:n.push(i[z++],i[z++],i[z++],i[z++],s=i[z++],h=i[z++]);break;case ih.Q:X=i[z++],q=i[z++],e=i[z++],r=i[z++],n.push(s+2/3*(X-s),h+2/3*(q-h),e+2/3*(X-e),r+2/3*(q-r),e,r),s=e,h=r;break;case ih.A:var R=i[z++],F=i[z++],B=i[z++],N=i[z++],H=i[z++],E=i[z++]+H;z+=1;var W=!i[z++],X=Math.cos(H)*B+R,q=Math.sin(H)*N+F;O?c(l=X,u=q):p(s,h,X,q),s=Math.cos(E)*B+R,h=Math.sin(E)*N+F;for(var Y=(W?-1:1)*Math.PI/2,j=H;W?E<j:j<E;j+=Y){var V=W?Math.max(j+Y,E):Math.min(j+Y,E);f=j,d=V,y=R,v=F,g=B,_=N,D=L=A=M=P=C=T=k=S=b=w=x=m=void 0,m=Math.abs(d-f),x=4*Math.tan(m/4)/3,w=d<f?-1:1,b=Math.cos(f),S=Math.sin(f),k=Math.cos(d),T=Math.sin(d),C=b*g+y,P=S*_+v,M=k*g+y,A=T*_+v,L=g*x*w,D=_*x*w,n.push(C-L*S,P+D*b,M+L*T,A-D*k,M,A)}break;case ih.R:l=s=i[z++],u=h=i[z++],X=l+i[z++],q=u+i[z++],c(X,u),p(X,u,X,q),p(X,q,l,q),p(l,q,l,u),p(l,u,X,u);break;case ih.Z:n&&p(s,h,l,u),s=l,h=u}}return n&&2<n.length&&a.push(n),a}function ah(t,e){var r=oh(t),i=[];e=e||1;for(var n=0;n<r.length;n++){var o=r[n],a=[],s=o[0],h=o[1];a.push(s,h);for(var l=2;l<o.length;){var u=o[l++],c=o[l++],p=o[l++],f=o[l++],d=o[l++],y=o[l++];!function t(e,r,i,n,o,a,s,h,l,u){var c,p,f,d,y,v,g,_,m,x,w,b,S,k,T;nh(e,i)&&nh(r,n)&&nh(o,s)&&nh(a,h)?l.push(s,h):(p=(c=2/u)*c,f=s-e,d=h-r,f/=y=Math.sqrt(f*f+d*d),d/=y,w=(_=o-s)*_+(m=a-h)*m,(x=(v=i-e)*v+(g=n-r)*g)<p&&w<p?l.push(s,h):(S=-f*_-d*m,x-(b=f*v+d*g)*b<p&&0<=b&&w-S*S<p&&0<=S?l.push(s,h):(T=[],vr(e,i,o,s,.5,k=[]),vr(r,n,a,h,.5,T),t(k[0],T[0],k[1],T[1],k[2],T[2],k[3],T[3],l,u),t(k[4],T[4],k[5],T[5],k[6],T[6],k[7],T[7],l,u))))}(s,h,u,c,p,f,d,y,a,e),s=d,h=y}i.push(a)}return i}function sh(t,e,r){var i=t[e],n=t[1-e],o=Math.abs(i/n),a=Math.ceil(Math.sqrt(o*r)),s=Math.floor(r/a);0===s&&(s=1,a=r);for(var h=[],l=0;l<a;l++)h.push(s);var u=r-a*s;if(0<u)for(l=0;l<u;l++)h[l%a]+=1;return h}function hh(t,e,r){for(var i=t.r0,n=t.r,o=t.startAngle,a=t.endAngle,s=Math.abs(a-o),h=s*n,l=n-i,u=h>Math.abs(l),c=sh([h,l],u?0:1,e),p=(u?s:l)/c.length,f=0;f<c.length;f++)for(var d=(u?l:s)/c[f],y=0;y<c[f];y++){var v={};u?(v.startAngle=o+p*f,v.endAngle=o+p*(f+1),v.r0=i+d*y,v.r=i+d*(y+1)):(v.startAngle=o+d*y,v.endAngle=o+d*(y+1),v.r0=i+p*f,v.r=i+p*(f+1)),v.clockwise=t.clockwise,v.cx=t.cx,v.cy=t.cy,r.push(v)}}function lh(t,e){var r=t[t.length-1];r&&r[0]===e[0]&&r[1]===e[1]||t.push(e)}function uh(t,e,r){for(var i=t.length,n=[],o=0;o<i;o++){var a=t[o],s=t[(o+1)%i],h=function(t,e,r,i,n,o,a,s){var h=r-t,l=i-e,u=a-n,c=s-o,p=u*l-h*c;if(Math.abs(p)<1e-6)return null;var f=((t-n)*c-u*(e-o))/p;return f<0||1<f?null:new fe(f*h+t,f*l+e)}(a[0],a[1],s[0],s[1],e.x,e.y,r.x,r.y);h&&n.push({projPt:function(t,e,r){var i=new fe;fe.sub(i,r,e),i.normalize();var n=new fe;return fe.sub(n,t,e),n.dot(i)}(h,e,r),pt:h,idx:o})}if(n.length<2)return[{points:t},{points:t}];n.sort(function(t,e){return t.projPt-e.projPt});var l,u=n[0],c=n[n.length-1];c.idx<u.idx&&(l=u,u=c,c=l);for(var p=[u.pt.x,u.pt.y],f=[c.pt.x,c.pt.y],d=[p],y=[f],o=u.idx+1;o<=c.idx;o++)lh(d,t[o].slice());lh(d,f),lh(d,p);for(o=c.idx+1;o<=u.idx+i;o++)lh(y,t[o%i].slice());return lh(y,p),lh(y,f),[{points:d},{points:y}]}function ch(t){var e=t.points,r=[],i=[];co(e,r,i);var n=new Se(r[0],r[1],i[0]-r[0],i[1]-r[1]),o=n.width,a=n.height,s=n.x,h=n.y,l=new fe,u=new fe;return a<o?(l.x=u.x=s+o/2,l.y=h,u.y=h+a):(l.y=u.y=h+a/2,l.x=s,u.x=s+o),uh(e,l,u)}function ph(t,e,r,i){var n,o;return 1===r?i.push(e):(n=Math.floor(r/2),o=t(e),ph(t,o[0],n,i),ph(t,o[1],r-n,i)),i}function fh(t,e){var r,i=[],n=t.shape;switch(t.type){case"rect":!function(t,e,r){for(var i=t.width,n=t.height,o=n<i,a=sh([i,n],o?0:1,e),s=o?"width":"height",h=o?"height":"width",l=o?"x":"y",u=o?"y":"x",c=t[s]/a.length,p=0;p<a.length;p++)for(var f=t[h]/a[p],d=0;d<a[p];d++){var y={};y[l]=p*c,y[u]=d*f,y[s]=c,y[h]=f,y.x+=t.x,y.y+=t.y,r.push(y)}}(n,e,i),r=Wa;break;case"sector":hh(n,e,i),r=eh;break;case"circle":hh({r0:0,r:n.r,startAngle:0,endAngle:2*Math.PI,cx:n.cx,cy:n.cy},e,i),r=eh;break;default:var o=t.getComputedTransform(),a=o?Math.sqrt(Math.max(o[0]*o[0]+o[1]*o[1],o[2]*o[2]+o[3]*o[3])):1,s=L(ah(t.getUpdatedPathProxy(),a),function(t){for(var e=[],r=0;r<t.length;)e.push([t[r++],t[r++]]);return e}),h=s.length;if(0===h)ph(ch,{points:s[0]},e,i);else if(h===e)for(var l=0;l<h;l++)i.push({points:s[l]});else{var u=0,c=L(s,function(t){var e=[],r=[];co(t,e,r);var i=(r[1]-e[1])*(r[0]-e[0]);return u+=i,{poly:t,area:i}});c.sort(function(t,e){return e.area-t.area});for(var p=e,l=0;l<h;l++){var f=c[l];if(p<=0)break;var d=l===h-1?p:Math.ceil(f.area/u*e);d<0||(ph(ch,{points:f.poly},d,i),p-=d)}}r=es}if(!r)return function(t,e){for(var r=[],i=0;i<e;i++)r.push(Ta(t));return r}(t,e);for(var y,v,g=[],l=0;l<i.length;l++){var _=new r;_.setShape(i[l]),y=t,(v=_).setStyle(y.style),v.z=y.z,v.z2=y.z2,v.zlevel=y.zlevel,g.push(_)}return g}function dh(t,e){for(var r=t.length,i=t[r-2],n=t[r-1],o=[],a=0;a<e.length;)o[a++]=i,o[a++]=n;return o}function yh(t,e){for(var r,i,n,o=[],a=[],s=0;s<Math.max(t.length,e.length);s++){var h=t[s],l=e[s],u=void 0,c=void 0;h?l?(i=u=(r=function(t,e){var r=t.length,i=e.length;if(r===i)return[t,e];for(var n=[],o=[],a=r<i?t:e,s=Math.min(r,i),h=Math.abs(i-r)/6,l=(s-2)/6,u=Math.ceil(h/l)+1,c=[a[0],a[1]],p=h,f=2;f<s;){var d=a[f-2],y=a[f-1],v=a[f++],g=a[f++],_=a[f++],m=a[f++],x=a[f++],w=a[f++];if(p<=0)c.push(v,g,_,m,x,w);else{for(var b=Math.min(p,u-1)+1,S=1;S<=b;S++){var k=S/b;vr(d,v,_,x,k,n),vr(y,g,m,w,k,o),d=n[3],y=o[3],c.push(n[1],o[1],n[2],o[2],d,y),v=n[5],g=o[5],_=n[6],m=o[6]}p-=b-1}}return a===t?[c,e]:[t,c]}(h,l))[0],n=c=r[1]):(c=dh(n||h,h),u=h):(u=dh(i||l,l),c=l),o.push(u),a.push(c)}return[o,a]}function vh(t){for(var e=0,r=0,i=0,n=t.length,o=0,a=n-2;o<n;a=o,o+=2){var s=t[a],h=t[a+1],l=t[o],u=t[o+1],c=s*u-l*h;e+=c,r+=(s+l)*c,i+=(h+u)*c}return 0===e?[t[0]||0,t[1]||0]:[r/e/3,i/e/3,e]}function gh(t,e,r,i){for(var n,o=[],a=0;a<t.length;a++){var s=t[a],h=e[a],l=vh(s),u=vh(h);null==n&&(n=l[2]<0!=u[2]<0);var c=[],p=[],f=0,d=1/0,y=[],v=s.length;n&&(s=function(t){for(var e=[],r=t.length,i=0;i<r;i+=2)e[i]=t[r-i-2],e[i+1]=t[r-i-1];return e}(s));for(var g=6*function(t,e,r,i){for(var n=(t.length-2)/6,o=1/0,a=0,s=t.length,h=s-2,l=0;l<n;l++){for(var u=6*l,c=0,p=0;p<s;p+=2){var f=0===p?u:(u+p-2)%h+2,d=t[f]-r[0],y=t[1+f]-r[1],v=e[p]-i[0]-d,g=e[p+1]-i[1]-y;c+=v*v+g*g}c<o&&(o=c,a=l)}return a}(s,h,l,u),_=v-2,m=0;m<_;m+=2){var x=(g+m)%_+2;c[m+2]=s[x]-l[0],c[m+3]=s[1+x]-l[1]}if(c[0]=s[g]-l[0],c[1]=s[1+g]-l[1],0<r)for(var w=i/r,b=-i/2;b<=i/2;b+=w){for(var S=Math.sin(b),k=Math.cos(b),T=0,m=0;m<s.length;m+=2){var C=c[m],P=c[m+1],M=h[m]-u[0],A=h[m+1]-u[1],L=M*k-A*S,D=M*S+A*k,z=(y[m]=L)-C,I=(y[m+1]=D)-P;T+=z*z+I*I}if(T<d){d=T,f=b;for(var O=0;O<y.length;O++)p[O]=y[O]}}else for(var R=0;R<v;R+=2)p[R]=h[R]-u[0],p[R+1]=h[R+1]-u[1];o.push({from:c,to:p,fromCp:l,toCp:u,rotation:-f})}return o}function _h(t){return t.__isCombineMorphing}var mh="__mOriginal_";function xh(t,e,r){var i=mh+e,n=t[i]||t[e];t[i]||(t[i]=t[e]);var o=r.replace,a=r.after,s=r.before;t[e]=function(){var t,e=arguments;return s&&s.apply(this,e),t=o?o.apply(this,e):n.apply(this,e),a&&a.apply(this,e),t}}function wh(t,e){var r=mh+e;t[r]&&(t[e]=t[r],t[r]=null)}function bh(t,e){for(var r=0;r<t.length;r++)for(var i=t[r],n=0;n<i.length;){var o=i[n],a=i[n+1];i[n++]=e[0]*o+e[2]*a+e[4],i[n++]=e[1]*o+e[3]*a+e[5]}}function Sh(t,C){var e=t.getUpdatedPathProxy(),r=C.getUpdatedPathProxy(),i=yh(oh(e),oh(r)),n=i[0],o=i[1],a=t.getComputedTransform(),s=C.getComputedTransform();a&&bh(n,a),s&&bh(o,s),xh(C,"updateTransform",{replace:function(){this.transform=null}}),C.transform=null;var P=gh(n,o,10,Math.PI),M=[];xh(C,"buildPath",{replace:function(t){for(var e=C.__morphT,r=1-e,i=[],n=0;n<P.length;n++){var o=P[n],a=o.from,s=o.to,h=o.rotation*e,l=o.fromCp,u=o.toCp,c=Math.sin(h),p=Math.cos(h);Mt(i,l,u,e);for(var f=0;f<a.length;f+=2){var d=a[f],y=a[f+1],v=d*r+(x=s[f])*e,g=y*r+(w=s[f+1])*e;M[f]=v*p-g*c+i[0],M[f+1]=v*c+g*p+i[1]}var _=M[0],m=M[1];t.moveTo(_,m);for(f=2;f<a.length;){var x=M[f++],w=M[f++],b=M[f++],S=M[f++],k=M[f++],T=M[f++];_===x&&m===w&&b===k&&S===T?t.lineTo(k,T):t.bezierCurveTo(x,w,b,S,k,T),_=k,m=T}}}})}function kh(t,e,r){if(!t||!e)return e;var i=r.done,n=r.during;return Sh(t,e),e.__morphT=0,e.animateTo({__morphT:1},k({during:function(t){e.dirtyShape(),n&&n(t)},done:function(){wh(e,"buildPath"),wh(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape(),i&&i()}},r)),e}function Th(r){var o=1/0,a=1/0,s=-1/0,h=-1/0,t=L(r,function(t){var e=t.getBoundingRect(),r=t.getComputedTransform(),i=e.x+e.width/2+(r?r[4]:0),n=e.y+e.height/2+(r?r[5]:0);return o=Math.min(i,o),a=Math.min(n,a),s=Math.max(i,s),h=Math.max(n,h),[i,n]});return L(t,function(t,e){return{cp:t,z:function(t,e,r,i,n,o){t=n===r?0:Math.round(32767*(t-r)/(n-r)),e=o===i?0:Math.round(32767*(e-i)/(o-i));for(var a,s=0,h=32768;0<h;h/=2){var l=0,u=0;0<(t&h)&&(l=1),0<(e&h)&&(u=1),s+=h*h*(3*l^u),0===u&&(1===l&&(t=h-1-t,e=h-1-e),a=t,t=e,e=a)}return s}(t[0],t[1],o,a,s,h),path:r[e]}}).sort(function(t,e){return t.z-e.z}).map(function(t){return t.path})}function Ch(t){return fh(t.path,t.count)}function Ph(){return{fromIndividuals:[],toIndividuals:[],count:0}}var Mh,Ah=Object.freeze({__proto__:null,alignBezierCurves:yh,centroid:vh,isCombineMorphing:_h,isMorphing:function(t){return 0<=t.__morphT},morphPath:kh,combineMorph:function(e,i,t){var n=[];!function t(e){for(var r=0;r<e.length;r++){var i=e[r];_h(i)?t(i.childrenRef()):i instanceof ra&&n.push(i)}}(e);var r=n.length;if(!r)return Ph();var o=(t.dividePath||Ch)({path:i,count:r});if(o.length!==r)return console.error("Invalid morphing: unmatched splitted path"),Ph();n=Th(n),o=Th(o);for(var a=t.done,s=t.during,h=t.individualDelay,l=new fn,u=0;u<r;u++){var c=n[u],p=o[u];p.parent=i,p.copyTransform(l),h||Sh(c,p)}function f(t){for(var e=0;e<o.length;e++)o[e].addSelfToZr(t)}function d(){i.__isCombineMorphing=!1,i.__morphT=-1,i.childrenRef=null,wh(i,"addSelfToZr"),wh(i,"removeSelfFromZr")}i.__isCombineMorphing=!0,i.childrenRef=function(){return o},xh(i,"addSelfToZr",{after:function(t){f(t)}}),xh(i,"removeSelfFromZr",{after:function(t){for(var e=0;e<o.length;e++)o[e].removeSelfFromZr(t)}});var y=o.length;if(h)for(var v=y,g=function(){0===--v&&(d(),a&&a())},u=0;u<y;u++){var _=h?k({delay:(t.delay||0)+h(u,y,n[u],o[u]),done:g},t):t;kh(n[u],o[u],_)}else i.__morphT=0,i.animateTo({__morphT:1},k({during:function(t){for(var e=0;e<y;e++){var r=o[e];r.__morphT=i.__morphT,r.dirtyShape()}s&&s(t)},done:function(){d();for(var t=0;t<e.length;t++)wh(e[t],"updateTransform");a&&a()}},t));return i.__zr&&f(i.__zr),{fromIndividuals:n,toIndividuals:o,count:y}},separateMorph:function(t,e,r){var i=e.length,n=[],o=r.dividePath||Ch;if(_h(t)){!function t(e){for(var r=0;r<e.length;r++){var i=e[r];_h(i)?t(i.childrenRef()):i instanceof ra&&n.push(i)}}(t.childrenRef());var a=n.length;if(a<i)for(var s=0,h=a;h<i;h++)n.push(Ta(n[s++%a]));n.length=i}else{n=o({path:t,count:i});for(var l=t.getComputedTransform(),h=0;h<n.length;h++)n[h].setLocalTransform(l);if(n.length!==i)return console.error("Invalid morphing: unmatched splitted path"),Ph()}n=Th(n),e=Th(e);for(var u=r.individualDelay,h=0;h<i;h++){var c=u?k({delay:(r.delay||0)+u(h,i,n[h],e[h])},r):r;kh(n[h],e[h],c)}return{fromIndividuals:n,toIndividuals:e,count:e.length}},defaultDividePath:fh}),Lh=(yt(Dh,Mh=ra),Dh.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),r=0;r<t.length;r++)e=e||t[r].shapeChanged();e&&this.dirtyShape()},Dh.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),r=0;r<t.length;r++)t[r].path||t[r].createPathProxy(),t[r].path.setScale(e[0],e[1],t[r].segmentIgnoreThreshold)},Dh.prototype.buildPath=function(t,e){for(var r=e.paths||[],i=0;i<r.length;i++)r[i].buildPath(t,r[i].shape,!0)},Dh.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},Dh.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),ra.prototype.getBoundingRect.call(this)},Dh);function Dh(){var t=null!==Mh&&Mh.apply(this,arguments)||this;return t.type="compound",t}var zh,Ih=[],Oh=(yt(Rh,zh=Jn),Rh.prototype.traverse=function(t,e){t.call(e,this)},Rh.prototype.useStyle=function(){this.style={}},Rh.prototype.getCursor=function(){return this._cursor},Rh.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},Rh.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},Rh.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},Rh.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},Rh.prototype.addDisplayables=function(t,e){e=e||!1;for(var r=0;r<t.length;r++)this.addDisplayable(t[r],e)},Rh.prototype.getDisplayables=function(){return this._displayables},Rh.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},Rh.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Rh.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},Rh.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Se(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var r=this._displayables[e],i=r.getBoundingRect().clone();r.needLocalTransform()&&i.applyTransform(r.getLocalTransform(Ih)),t.union(i)}this._rect=t}return this._rect},Rh.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(r[0],r[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},Rh);function Rh(){var t=null!==zh&&zh.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var Fh=new Mr(50);function Bh(t,e,r,i,n){if(t){if("string"!=typeof t)return t;if(e&&e.__zrImageSrc===t||!r)return e;var o=Fh.get(t),a={hostEl:r,cb:i,cbPayload:n};return o?Hh(e=o.image)||o.pending.push(a):((e=f.loadImage(t,Nh,Nh)).__zrImageSrc=t,Fh.put(t,e.__cachedImgObj={image:e,pending:[a]})),e}return e}function Nh(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var r=t.pending[e],i=r.cb;i&&i(this,r.cbPayload),r.hostEl.dirty()}t.pending.length=0}function Hh(t){return t&&t.width&&t.height}var Eh=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Wh(t,e,r,i){var n=I({},i=i||{});n.font=e,r=Z(r,"..."),n.maxIterations=Z(i.maxIterations,2);var o=n.minChar=Z(i.minChar,0);n.cnCharWidth=_n("国",e);var a=n.ascCharWidth=_n("a",e);n.placeholder=Z(i.placeholder,"");for(var s=t=Math.max(0,t-1),h=0;h<o&&a<=s;h++)s-=a;var l=_n(r,e);return s<l&&(r="",l=0),s=t-l,n.ellipsis=r,n.ellipsisWidth=l,n.contentWidth=s,n.containerWidth=t,n}function Xh(t,e,r){var i=r.containerWidth,n=r.font,o=r.contentWidth;if(!i)return t.textLine="",void(t.isTruncated=!1);if((h=_n(e,n))<=i)return t.textLine=e,void(t.isTruncated=!1);for(var a=0;;a++){if(h<=o||a>=r.maxIterations){e+=r.ellipsis;break}var s=0===a?function(t,e,r,i){for(var n=0,o=0,a=t.length;o<a&&n<e;o++){var s=t.charCodeAt(o);n+=0<=s&&s<=127?r:i}return o}(e,o,r.ascCharWidth,r.cnCharWidth):0<h?Math.floor(e.length*o/h):0,h=_n(e=e.substr(0,s),n)}""===e&&(e=r.placeholder),t.textLine=e,t.isTruncated=!0}var qh=function(){},Yh=function(t){this.tokens=[],t&&(this.tokens=t)},jh=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1};function Vh(t,e){var r=new jh;if(null!=t&&(t+=""),!t)return r;for(var i,n=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==n?null:{width:n,accumWidth:0,breakAll:"breakAll"===a},h=Eh.lastIndex=0;null!=(i=Eh.exec(t));){var l=i.index;h<l&&Uh(r,t.substring(h,l),e,s),Uh(r,i[2],e,s,i[1]),h=Eh.lastIndex}h<t.length&&Uh(r,t.substring(h,t.length),e,s);var u=[],c=0,p=0,f=e.padding,d="truncate"===a,y="truncate"===e.lineOverflow,v={};function g(t,e,r){t.width=e,t.lineHeight=r,c+=r,p=Math.max(p,e)}t:for(var _=0;_<r.lines.length;_++){for(var m=r.lines[_],x=0,w=0,b=0;b<m.tokens.length;b++){var S=(O=m.tokens[b]).styleName&&e.rich[O.styleName]||{},k=O.textPadding=S.padding,T=k?k[1]+k[3]:0,C=O.font=S.font||e.font;O.contentHeight=bn(C);var P=Z(S.height,O.contentHeight);if(O.innerHeight=P,k&&(P+=k[0]+k[2]),O.height=P,O.lineHeight=K(S.lineHeight,e.lineHeight,P),O.align=S&&S.align||e.align,O.verticalAlign=S&&S.verticalAlign||"middle",y&&null!=o&&c+O.lineHeight>o){var M=r.lines.length;0<b?(m.tokens=m.tokens.slice(0,b),g(m,w,x),r.lines=r.lines.slice(0,_+1)):r.lines=r.lines.slice(0,_),r.isTruncated=r.isTruncated||r.lines.length<M;break t}var A,L,D,z=S.width,I=null==z||"auto"===z;"string"==typeof z&&"%"===z.charAt(z.length-1)?(O.percentWidth=z,u.push(O),O.contentWidth=_n(O.text,C)):(!I||(L=(A=S.backgroundColor)&&A.image)&&Hh(L=function(t){if("string"!=typeof t)return t;var e=Fh.get(t);return e&&e.image}(L))&&(O.width=Math.max(O.width,L.width*P/L.height)),null!=(D=d&&null!=n?n-w:null)&&D<O.width?!I||D<T?(O.text="",O.width=O.contentWidth=0):(function(t,e,r,i,n,o){if(!r)return t.text="",t.isTruncated=!1;var a=(e+"").split("\n");o=Wh(r,i,n,o);for(var s=!1,h={},l=0,u=a.length;l<u;l++)Xh(h,a[l],o),a[l]=h.textLine,s=s||h.isTruncated;t.text=a.join("\n"),t.isTruncated=s}(v,O.text,D-T,C,e.ellipsis,{minChar:e.truncateMinChar}),O.text=v.text,r.isTruncated=r.isTruncated||v.isTruncated,O.width=O.contentWidth=_n(O.text,C)):O.contentWidth=_n(O.text,C)),O.width+=T,w+=O.width,S&&(x=Math.max(x,O.lineHeight))}g(m,w,x)}r.outerWidth=r.width=Z(n,p),r.outerHeight=r.height=Z(o,c),r.contentHeight=c,r.contentWidth=p,f&&(r.outerWidth+=f[1]+f[3],r.outerHeight+=f[0]+f[2]);for(_=0;_<u.length;_++){var O,R=(O=u[_]).percentWidth;O.width=parseInt(R,10)/100*r.width}return r}function Uh(t,e,r,i,n){var o,a,s,h,l,u,c=""===e,p=n&&r.rich[n]||{},f=t.lines,d=p.font||r.font,y=!1;i?(h=(s=p.padding)?s[1]+s[3]:0,null!=p.width&&"auto"!==p.width?(l=Sn(p.width,i.width)+h,0<f.length&&l+i.accumWidth>i.width&&(o=e.split("\n"),y=!0),i.accumWidth=l):(u=Zh(e,d,i.width,i.breakAll,i.accumWidth),i.accumWidth=u.accumWidth+h,a=u.linesWidths,o=u.lines)):o=e.split("\n");for(var v=0;v<o.length;v++){var g,_,m=o[v],x=new qh;x.styleName=n,x.text=m,x.isLineHolder=!m&&!c,x.width="number"==typeof p.width?p.width:a?a[v]:_n(m,d),v||y?f.push(new Yh([x])):1===(_=(g=(f[f.length-1]||(f[0]=new Yh)).tokens).length)&&g[0].isLineHolder?g[0]=x:!m&&_&&!c||g.push(x)}}var Gh=D(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function Zh(t,e,r,i,n){for(var o,a,s=[],h=[],l="",u="",c=0,p=0,f=0;f<t.length;f++){var d,y,v=t.charAt(f);"\n"!==v?(d=_n(v,e),y=!i&&(a=void 0,!(!(32<=(a=(o=v).charCodeAt(0))&&a<=591||880<=a&&a<=4351||4608<=a&&a<=5119||7680<=a&&a<=8303)||Gh[o])),(s.length?r<p+d:r<n+p+d)?p?(l||u)&&(p=y?(l||(l=u,u="",p=c=0),s.push(l),h.push(p-c),u+=v,l="",c+=d):(u&&(l+=u,u="",c=0),s.push(l),h.push(p),l=v,d)):y?(s.push(u),h.push(c),u=v,c=d):(s.push(v),h.push(d)):(p+=d,y?(u+=v,c+=d):(u&&(l+=u,u="",c=0),l+=v))):(u&&(l+=u,p+=c),s.push(l),h.push(p),u=l="",p=c=0)}return s.length||l||(l=t,u="",c=0),u&&(l+=u),l&&(s.push(l),h.push(p)),1===s.length&&(p+=n),{accumWidth:p,lines:s,linesWidths:h}}var Kh,Qh={fill:"#000"},$h={style:k({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},Gn.style)},Jh=(yt(tl,Kh=Jn),tl.prototype.childrenRef=function(){return this._children},tl.prototype.update=function(){Kh.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},tl.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):Kh.prototype.updateTransform.call(this)},tl.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):Kh.prototype.getLocalTransform.call(this,t)},tl.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),Kh.prototype.getComputedTransform.call(this)},tl.prototype._updateSubTexts=function(){var t;this._childCursor=0,sl(t=this.style),O(t.rich,sl),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},tl.prototype.addSelfToZr=function(t){Kh.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},tl.prototype.removeSelfFromZr=function(t){Kh.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},tl.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new Se(0,0,0,0),e=this._children,r=[],i=null,n=0;n<e.length;n++){var o=e[n],a=o.getBoundingRect(),s=o.getLocalTransform(r);s?(t.copy(a),t.applyTransform(s),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},tl.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Qh},tl.prototype.setTextContent=function(t){},tl.prototype._mergeStyle=function(t,e){if(!e)return t;var r=e.rich,i=t.rich||r&&{};return I(t,e),r&&i?(this._mergeRich(i,r),t.rich=i):i&&(t.rich=i),t},tl.prototype._mergeRich=function(t,e){for(var r=F(e),i=0;i<r.length;i++){var n=r[i];t[n]=t[n]||{},I(t[n],e[n])}},tl.prototype.getAnimationStyleProps=function(){return $h},tl.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},tl.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||E,r=t.padding,i=function(t,e){null!=t&&(t+="");var r,i,n=e.overflow,o=e.padding,a=e.font,s="truncate"===n,h=bn(a),l=Z(e.lineHeight,h),u=!!e.backgroundColor,c="truncate"===e.lineOverflow,p=!1,f=e.width,d=(i=null==f||"break"!==n&&"breakAll"!==n?t?t.split("\n"):[]:t?Zh(t,e.font,f,"breakAll"===n,0).lines:[]).length*l,y=Z(e.height,d);if(y<d&&c&&(r=Math.floor(y/l),p=p||i.length>r,i=i.slice(0,r)),t&&s&&null!=f)for(var v=Wh(f,a,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),g={},_=0;_<i.length;_++)Xh(g,i[_],v),i[_]=g.textLine,p=p||g.isTruncated;for(var m=y,x=0,_=0;_<i.length;_++)x=Math.max(_n(i[_],a),x);null==f&&(f=x);var w=x;return o&&(m+=o[0]+o[2],w+=o[1]+o[3],f+=o[1]+o[3]),u&&(w=f),{lines:i,height:y,outerWidth:w,outerHeight:m,lineHeight:l,calculatedLineHeight:h,contentWidth:x,contentHeight:d,width:f,isTruncated:p}}(cl(t),t),n=pl(t),o=!!t.backgroundColor,a=i.outerHeight,s=i.outerWidth,h=i.contentWidth,l=i.lines,u=i.lineHeight,c=this._defaultStyle;this.isTruncated=!!i.isTruncated;var p,f,d=t.x||0,y=t.y||0,v=t.align||c.align||"left",g=t.verticalAlign||c.verticalAlign||"top",_=d,m=wn(y,i.contentHeight,g);(n||r)&&(p=xn(d,s,v),f=wn(y,a,g),n&&this._renderBackground(t,t,p,f,s,a)),m+=u/2,r&&(_=ul(d,v,r),"top"===g?m+=r[0]:"bottom"===g&&(m-=r[2]));for(var x=0,w=!1,b=(ll("fill"in t?t.fill:(w=!0,c.fill))),S=(hl("stroke"in t?t.stroke:o||c.autoStroke&&!w?null:(x=2,c.stroke))),k=0<t.textShadowBlur,T=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),C=i.calculatedLineHeight,P=0;P<l.length;P++){var M=this._getOrCreateChild(_s),A=M.createStyle();M.useStyle(A),A.text=l[P],A.x=_,A.y=m,v&&(A.textAlign=v),A.textBaseline="middle",A.opacity=t.opacity,A.strokeFirst=!0,k&&(A.shadowBlur=t.textShadowBlur||0,A.shadowColor=t.textShadowColor||"transparent",A.shadowOffsetX=t.textShadowOffsetX||0,A.shadowOffsetY=t.textShadowOffsetY||0),A.stroke=S,A.fill=b,S&&(A.lineWidth=t.lineWidth||x,A.lineDash=t.lineDash,A.lineDashOffset=t.lineDashOffset||0),A.font=e,ol(A,t),m+=u,T&&M.setBoundingRect(new Se(xn(A.x,h,A.textAlign),wn(A.y,C,A.textBaseline),h,C))}},tl.prototype._updateRichTexts=function(){var t=this.style,e=Vh(cl(t),t),r=e.width,i=e.outerWidth,n=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,h=this._defaultStyle,l=t.align||h.align,u=t.verticalAlign||h.verticalAlign;this.isTruncated=!!e.isTruncated;var c=xn(a,i,l),p=wn(s,n,u),f=c,d=p;o&&(f+=o[3],d+=o[0]);var y=f+r;pl(t)&&this._renderBackground(t,t,c,p,i,n);for(var v=!!t.backgroundColor,g=0;g<e.lines.length;g++){for(var _=e.lines[g],m=_.tokens,x=m.length,w=_.lineHeight,b=_.width,S=0,k=f,T=y,C=x-1,P=void 0;S<x&&(!(P=m[S]).align||"left"===P.align);)this._placeToken(P,t,w,d,k,"left",v),b-=P.width,k+=P.width,S++;for(;0<=C&&"right"===(P=m[C]).align;)this._placeToken(P,t,w,d,T,"right",v),b-=P.width,T-=P.width,C--;for(k+=(r-(k-f)-(y-T)-b)/2;S<=C;)P=m[S],this._placeToken(P,t,w,d,k+P.width/2,"center",v),k+=P.width,S++;d+=w}},tl.prototype._placeToken=function(t,e,r,i,n,o,a){var s=e.rich[t.styleName]||{};s.text=t.text;var h=t.verticalAlign,l=i+r/2;"top"===h?l=i+t.height/2:"bottom"===h&&(l=i+r-t.height/2),!t.isLineHolder&&pl(s)&&this._renderBackground(s,e,"right"===o?n-t.width:"center"===o?n-t.width/2:n,l-t.height/2,t.width,t.height);var u=!!s.backgroundColor,c=t.textPadding;c&&(n=ul(n,o,c),l-=t.height/2-c[0]-t.innerHeight/2);var p=this._getOrCreateChild(_s),f=p.createStyle();p.useStyle(f);var d=this._defaultStyle,y=!1,v=0,g=ll("fill"in s?s.fill:"fill"in e?e.fill:(y=!0,d.fill)),_=hl("stroke"in s?s.stroke:"stroke"in e?e.stroke:u||a||d.autoStroke&&!y?null:(v=2,d.stroke)),m=0<s.textShadowBlur||0<e.textShadowBlur;f.text=t.text,f.x=n,f.y=l,m&&(f.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,f.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",f.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,f.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),f.textAlign=o,f.textBaseline="middle",f.font=t.font||E,f.opacity=K(s.opacity,e.opacity,1),ol(f,s),_&&(f.lineWidth=K(s.lineWidth,e.lineWidth,v),f.lineDash=Z(s.lineDash,e.lineDash),f.lineDashOffset=e.lineDashOffset||0,f.stroke=_),g&&(f.fill=g);var x=t.contentWidth,w=t.contentHeight;p.setBoundingRect(new Se(xn(f.x,x,f.textAlign),wn(f.y,w,f.textBaseline),x,w))},tl.prototype._renderBackground=function(t,e,r,i,n,o){var a,s,h,l,u,c=t.backgroundColor,p=t.borderWidth,f=t.borderColor,d=c&&c.image,y=c&&!d,v=t.borderRadius,g=this;(y||t.lineHeight||p&&f)&&((a=this._getOrCreateChild(Wa)).useStyle(a.createStyle()),a.style.fill=null,(h=a.shape).x=r,h.y=i,h.width=n,h.height=o,h.r=v,a.dirtyShape()),y?((u=a.style).fill=c||null,u.fillOpacity=Z(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(La)).onload=function(){g.dirtyStyle()},(l=s.style).image=c.image,l.x=r,l.y=i,l.width=n,l.height=o),p&&f&&((u=a.style).lineWidth=p,u.stroke=f,u.strokeOpacity=Z(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill()&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2));var _=(a||s).style;_.shadowBlur=t.shadowBlur||0,_.shadowColor=t.shadowColor||"transparent",_.shadowOffsetX=t.shadowOffsetX||0,_.shadowOffsetY=t.shadowOffsetY||0,_.opacity=K(t.opacity,e.opacity,1)},tl.makeFont=function(t){var e="";return al(t)&&(e=[t.fontStyle,t.fontWeight,nl(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&tt(e)||t.textFont||t.font},tl);function tl(t){var e=Kh.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=Qh,e.attr(t),e}var el={left:!0,right:1,center:1},rl={top:1,bottom:1,middle:1},il=["fontStyle","fontWeight","fontSize","fontFamily"];function nl(t){return"string"!=typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?h+"px":t+"px":t}function ol(t,e){for(var r=0;r<il.length;r++){var i=il[r],n=e[i];null!=n&&(t[i]=n)}}function al(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function sl(t){var e,r;t&&(t.font=Jh.makeFont(t),"middle"===(e=t.align)&&(e="center"),t.align=null==e||el[e]?e:"left","center"===(r=t.verticalAlign)&&(r="middle"),t.verticalAlign=null==r||rl[r]?r:"top",t.padding&&(t.padding=$(t.padding)))}function hl(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function ll(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function ul(t,e,r){return"right"===e?t-r[1]:"center"===e?t+r[3]/2-r[1]/2:t+r[3]}function cl(t){var e=t.text;return null!=e&&(e+=""),e}function pl(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var fl,dl=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},yl=(yt(vl,fl=ra),vl.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},vl.prototype.getDefaultShape=function(){return new dl},vl.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,h=Math.cos(o),l=Math.sin(o);t.moveTo(h*n+r,l*n+i),t.arc(r,i,n,o,a,!s)},vl);function vl(t){return fl.call(this,t)||this}yl.prototype.type="arc";var gl=[],_l=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function ml(t,e,r){var i=t.cpx2,n=t.cpy2;return null!=i||null!=n?[(r?fr:pr)(t.x1,t.cpx1,t.cpx2,t.x2,e),(r?fr:pr)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(r?_r:gr)(t.x1,t.cpx1,t.x2,e),(r?_r:gr)(t.y1,t.cpy1,t.y2,e)]}var xl,wl=(yt(bl,xl=ra),bl.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},bl.prototype.getDefaultShape=function(){return new _l},bl.prototype.buildPath=function(t,e){var r=e.x1,i=e.y1,n=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,h=e.cpx2,l=e.cpy2,u=e.percent;0!==u&&(t.moveTo(r,i),null==h||null==l?(u<1&&(xr(r,a,n,u,gl),a=gl[1],n=gl[2],xr(i,s,o,u,gl),s=gl[1],o=gl[2]),t.quadraticCurveTo(a,s,n,o)):(u<1&&(vr(r,a,h,n,u,gl),a=gl[1],h=gl[2],n=gl[3],vr(i,s,l,o,u,gl),s=gl[1],l=gl[2],o=gl[3]),t.bezierCurveTo(a,s,h,l,n,o)))},bl.prototype.pointAt=function(t){return ml(this.shape,t,!1)},bl.prototype.tangentAt=function(t){var e=ml(this.shape,t,!0);return St(e,e)},bl);function bl(t){return xl.call(this,t)||this}wl.prototype.type="bezier-curve";var Sl,kl=function(){this.cx=0,this.cy=0,this.width=0,this.height=0},Tl=(yt(Cl,Sl=ra),Cl.prototype.getDefaultShape=function(){return new kl},Cl.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=e.width,o=e.height;t.moveTo(r,i+n),t.bezierCurveTo(r+n,i+n,r+3*n/2,i-n/3,r,i-o),t.bezierCurveTo(r-3*n/2,i-n/3,r-n,i+n,r,i+n),t.closePath()},Cl);function Cl(t){return Sl.call(this,t)||this}Tl.prototype.type="droplet";var Pl,Ml=function(){this.cx=0,this.cy=0,this.width=0,this.height=0},Al=(yt(Ll,Pl=ra),Ll.prototype.getDefaultShape=function(){return new Ml},Ll.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=e.width,o=e.height;t.moveTo(r,i),t.bezierCurveTo(r+n/2,i-2*o/3,r+2*n,i+o/3,r,i+o),t.bezierCurveTo(r-2*n,i+o/3,r-n/2,i-2*o/3,r,i)},Ll);function Ll(t){return Pl.call(this,t)||this}Al.prototype.type="heart";var Dl,zl=Math.PI,Il=Math.sin,Ol=Math.cos,Rl=function(){this.x=0,this.y=0,this.r=0,this.n=0},Fl=(yt(Bl,Dl=ra),Bl.prototype.getDefaultShape=function(){return new Rl},Bl.prototype.buildPath=function(t,e){var r=e.n;if(r&&!(r<2)){var i=e.x,n=e.y,o=e.r,a=2*zl/r,s=-zl/2;t.moveTo(i+o*Ol(s),n+o*Il(s));for(var h=0,l=r-1;h<l;h++)s+=a,t.lineTo(i+o*Ol(s),n+o*Il(s));t.closePath()}},Bl);function Bl(t){return Dl.call(this,t)||this}Fl.prototype.type="isogon";var Nl,Hl=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},El=(yt(Wl,Nl=ra),Wl.prototype.getDefaultShape=function(){return new Hl},Wl.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=2*Math.PI;t.moveTo(r+e.r,i),t.arc(r,i,e.r,0,n,!1),t.moveTo(r+e.r0,i),t.arc(r,i,e.r0,0,n,!0)},Wl);function Wl(t){return Nl.call(this,t)||this}El.prototype.type="ring";var Xl,ql=Math.sin,Yl=Math.cos,jl=Math.PI/180,Vl=function(){this.cx=0,this.cy=0,this.r=[],this.k=0,this.n=1},Ul=(yt(Gl,Xl=ra),Gl.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Gl.prototype.getDefaultShape=function(){return new Vl},Gl.prototype.buildPath=function(t,e){var r,i,n,o=e.r,a=e.k,s=e.n,h=e.cx,l=e.cy;t.moveTo(h,l);for(var u=0,c=o.length;u<c;u++){n=o[u];for(var p=0;p<=360*s;p++)r=n*ql(a/s*p%360*jl)*Yl(p*jl)+h,i=n*ql(a/s*p%360*jl)*ql(p*jl)+l,t.lineTo(r,i)}},Gl);function Gl(t){return Xl.call(this,t)||this}Ul.prototype.type="rose";var Zl,Kl=Math.PI,Ql=Math.cos,$l=Math.sin,Jl=function(){this.cx=0,this.cy=0,this.n=3,this.r=0},tu=(yt(eu,Zl=ra),eu.prototype.getDefaultShape=function(){return new Jl},eu.prototype.buildPath=function(t,e){var r=e.n;if(r&&!(r<2)){var i=e.cx,n=e.cy,o=e.r,a=e.r0;null==a&&(a=4<r?o*Ql(2*Kl/r)/Ql(Kl/r):o/3);var s=Kl/r,h=-Kl/2,l=i+o*Ql(h),u=n+o*$l(h);h+=s,t.moveTo(l,u);for(var c,p=0,f=2*r-1;p<f;p++)c=p%2==0?a:o,t.lineTo(i+c*Ql(h),n+c*$l(h)),h+=s;t.closePath()}},eu);function eu(t){return Zl.call(this,t)||this}tu.prototype.type="star";var ru,iu=Math.cos,nu=Math.sin,ou=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0,this.d=0,this.location="out"},au=(yt(su,ru=ra),su.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},su.prototype.getDefaultShape=function(){return new ou},su.prototype.buildPath=function(t,e){var r,i,n=e.r,o=e.r0,a=e.d,s=e.cx,h=e.cy,l="out"===e.location?1:-1;if(!(e.location&&n<=o)){var u,c=0,p=1,f=(n+l*o)*iu(0)-l*a*iu(0)+s,d=(n+l*o)*nu(0)-a*nu(0)+h;for(t.moveTo(f,d);o*++c%(n+l*o)!=0;);for(;u=Math.PI/180*p,r=(n+l*o)*iu(u)-l*a*iu((n/o+l)*u)+s,i=(n+l*o)*nu(u)-a*nu((n/o+l)*u)+h,t.lineTo(r,i),++p<=o*c/(n+l*o)*360;);}},su);function su(t){return ru.call(this,t)||this}au.prototype.type="trochoid";function hu(t,e){this.image=t,this.repeat=e,this.x=0,this.y=0,this.rotation=0,this.scaleX=1,this.scaleY=1}var lu=[0,0],uu=[0,0],cu=new fe,pu=new fe,fu=(du.prototype.fromBoundingRect=function(t,e){var r=this._corners,i=this._axes,n=t.x,o=t.y,a=n+t.width,s=o+t.height;if(r[0].set(n,o),r[1].set(a,o),r[2].set(a,s),r[3].set(n,s),e)for(var h=0;h<4;h++)r[h].transform(e);for(fe.sub(i[0],r[1],r[0]),fe.sub(i[1],r[3],r[0]),i[0].normalize(),i[1].normalize(),h=0;h<2;h++)this._origin[h]=i[h].dot(r[0])},du.prototype.intersect=function(t,e){var r=!0,i=!e;return cu.set(1/0,1/0),pu.set(0,0),!this._intersectCheckOneSide(this,t,cu,pu,i,1)&&(r=!1,i)||!this._intersectCheckOneSide(t,this,cu,pu,i,-1)&&(r=!1,i)||i||fe.copy(e,r?cu:pu),r},du.prototype._intersectCheckOneSide=function(t,e,r,i,n,o){for(var a=!0,s=0;s<2;s++){var h=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,lu),this._getProjMinMaxOnAxis(s,e._corners,uu),lu[1]<uu[0]||uu[1]<lu[0]){if(a=!1,n)return a;var l=Math.abs(uu[0]-lu[1]),u=Math.abs(lu[0]-uu[1]);Math.min(l,u)>i.len()&&(l<u?fe.scale(i,h,-l*o):fe.scale(i,h,u*o))}else r&&(l=Math.abs(uu[0]-lu[1]),u=Math.abs(lu[0]-uu[1]),Math.min(l,u)<r.len()&&(l<u?fe.scale(r,h,l*o):fe.scale(r,h,-u*o)))}return a},du.prototype._getProjMinMaxOnAxis=function(t,e,r){for(var i=this._axes[t],n=this._origin,o=e[0].dot(i)+n[t],a=o,s=o,h=1;h<e.length;h++)var l=e[h].dot(i)+n[t],a=Math.min(l,a),s=Math.max(l,s);r[0]=a,r[1]=s},du);function du(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var r=0;r<4;r++)this._corners[r]=new fe;for(r=0;r<2;r++)this._axes[r]=new fe;t&&this.fromBoundingRect(t,e)}var yu=(vu.prototype.update=function(t){var e=this.dom.style;e.width=t.width+"px",e.height=t.height+"px",e.left=t.x+"px",e.top=t.y+"px"},vu.prototype.hide=function(){this.dom.style.opacity="0"},vu.prototype.show=function(t){var e=this;clearTimeout(this._hideTimeout),this.dom.style.opacity="1",this._hideTimeout=setTimeout(function(){e.hide()},t||1e3)},vu);function vu(t){var e=this.dom=document.createElement("div");for(var r in e.className="ec-debug-dirty-rect",t=I({},t),I(t,{backgroundColor:"rgba(0, 0, 255, 0.2)",border:"1px solid #00f"}),e.style.cssText="\nposition: absolute;\nopacity: 0;\ntransition: opacity 0.5s linear;\npointer-events: none;\n",t)t.hasOwnProperty(r)&&(e.style[r]=t[r])}function gu(t){return isFinite(t)}function _u(t,e,r){for(var i,n,o,a,s,h,l,u,c,p,f,d,y,v,g,_,m="radial"===e.type?(u=t,c=e,f=(p=r).width,d=p.height,y=Math.min(f,d),v=null==c.x?.5:c.x,g=null==c.y?.5:c.y,_=null==c.r?.5:c.r,c.global||(v=v*f+p.x,g=g*d+p.y,_*=y),v=gu(v)?v:.5,g=gu(g)?g:.5,_=0<=_&&gu(_)?_:.5,u.createRadialGradient(v,g,0,v,g,_)):(i=t,o=r,a=null==(n=e).x?0:n.x,s=null==n.x2?1:n.x2,h=null==n.y?0:n.y,l=null==n.y2?0:n.y2,n.global||(a=a*o.width+o.x,s=s*o.width+o.x,h=h*o.height+o.y,l=l*o.height+o.y),a=gu(a)?a:0,s=gu(s)?s:1,h=gu(h)?h:0,l=gu(l)?l:0,i.createLinearGradient(a,h,s,l)),x=e.colorStops,w=0;w<x.length;w++)m.addColorStop(x[w].offset,x[w].color);return m}function mu(t){return parseInt(t,10)}function xu(t,e,r){var i=["width","height"][e],n=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],a=["paddingRight","paddingBottom"][e];if(null!=r[i]&&"auto"!==r[i])return parseFloat(r[i]);var s=document.defaultView.getComputedStyle(t);return(t[n]||mu(s[i])||mu(t.style[i]))-(mu(s[o])||0)-(mu(s[a])||0)|0}function wu(t){var e,r,i,n=t.style,o=n.lineDash&&0<n.lineWidth&&(e=n.lineDash,r=n.lineWidth,e&&"solid"!==e&&0<r?"dashed"===e?[4*r,2*r]:"dotted"===e?[r]:H(e)?[e]:G(e)?e:null:null),a=n.lineDashOffset;return!o||(i=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==i&&(o=L(o,function(t){return t/i}),a/=i),[o,a]}var bu=new Oo(!0);function Su(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function ku(t){return"string"==typeof t&&"none"!==t}function Tu(t){var e=t.fill;return null!=e&&"none"!==e}function Cu(t,e){var r;null!=e.fillOpacity&&1!==e.fillOpacity?(r=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=r):t.fill()}function Pu(t,e){var r;null!=e.strokeOpacity&&1!==e.strokeOpacity?(r=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=r):t.stroke()}function Mu(t,e,r){var i=Bh(e.image,e.__image,r);if(Hh(i)){var n,o=t.createPattern(i,e.repeat||"repeat");return"function"==typeof DOMMatrix&&o&&o.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*pt),n.scaleSelf(e.scaleX||1,e.scaleY||1),o.setTransform(n)),o}}var Au=["shadowBlur","shadowOffsetX","shadowOffsetY"],Lu=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Du(t,e,r,i,n){var o,a=!1;if(!i&&e===(r=r||{}))return!1;!i&&e.opacity===r.opacity||(Nu(t,n),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?Un.opacity:o),!i&&e.blend===r.blend||(a||(Nu(t,n),a=!0),t.globalCompositeOperation=e.blend||Un.blend);for(var s=0;s<Au.length;s++){var h=Au[s];!i&&e[h]===r[h]||(a||(Nu(t,n),a=!0),t[h]=t.dpr*(e[h]||0))}return!i&&e.shadowColor===r.shadowColor||(a||(Nu(t,n),a=!0),t.shadowColor=e.shadowColor||Un.shadowColor),a}function zu(t,e,r,i,n){var o=Hu(e,n.inHover),a=i?null:r&&Hu(r,n.inHover)||{};if(o!==a){var s,h=Du(t,o,a,i,n);!i&&o.fill===a.fill||(h||(Nu(t,n),h=!0),ku(o.fill)&&(t.fillStyle=o.fill)),!i&&o.stroke===a.stroke||(h||(Nu(t,n),h=!0),ku(o.stroke)&&(t.strokeStyle=o.stroke)),!i&&o.opacity===a.opacity||(h||(Nu(t,n),h=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(s=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==s&&(h||(Nu(t,n),h=!0),t.lineWidth=s));for(var l=0;l<Lu.length;l++){var u=Lu[l],c=u[0];!i&&o[c]===a[c]||(h||(Nu(t,n),h=!0),t[c]=o[c]||u[1])}return h}}function Iu(t,e){var r=e.transform,i=t.dpr||1;r?t.setTransform(i*r[0],i*r[1],i*r[2],i*r[3],i*r[4],i*r[5]):t.setTransform(i,0,0,i,0,0)}var Ou=1,Ru=2,Fu=3,Bu=4;function Nu(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function Hu(t,e){return e&&t.__hoverStyle||t.style}function Eu(t,e,r,i){var n=e.transform;if(!e.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1))return e.__dirty&=~Ve,void(e.__isRendered=!1);var o,a,s,h,l,u,c,p,f,d,y,v,g,_,m,x,w,b,S,k,T,C,P,M,A,L,D,z,I,O,R,F=e.__clipPaths,B=r.prevElClipPaths,N=!1,H=!1;B&&!function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return 1}}(F,B)||(B&&B.length&&(Nu(t,r),t.restore(),H=N=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),F&&F.length&&(Nu(t,r),t.save(),function(t,e,r){for(var i=!1,n=0;n<t.length;n++){var o=t[n],i=i||o.isZeroArea();Iu(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}r.allClipped=i}(F,t,r),N=!0),r.prevElClipPaths=F),r.allClipped?e.__isRendered=!1:(e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush(),(o=r.prevEl)||(H=N=!0),a=e instanceof ra&&e.autoBatch&&(s=e.style,h=Tu(s),l=Su(s),!(s.lineDash||!(+h^+l)||h&&"string"!=typeof s.fill||l&&"string"!=typeof s.stroke||s.strokePercent<1||s.strokeOpacity<1||s.fillOpacity<1)),N||(u=n,c=o.transform,u&&c?u[0]!==c[0]||u[1]!==c[1]||u[2]!==c[2]||u[3]!==c[3]||u[4]!==c[4]||u[5]!==c[5]:u||c)?(Nu(t,r),Iu(t,e)):a||Nu(t,r),p=Hu(e,r.inHover),e instanceof ra?(r.lastDrawType!==Ou&&(H=!0,r.lastDrawType=Ou),zu(t,e,o,H,r),a&&(r.batchFill||r.batchStroke)||t.beginPath(),function(t,e,r,i){var n,o=Su(r),a=Tu(r),s=r.strokePercent,h=s<1,l=!e.path;e.silent&&!h||!l||e.createPathProxy();var u,c,p,f,d,y,v,g,_,m,x,w=e.path||bu,b=e.__dirty;i||(u=r.fill,c=r.stroke,p=a&&!!u.colorStops,f=o&&!!c.colorStops,d=a&&!!u.image,y=o&&!!c.image,x=m=_=g=v=void 0,(p||f)&&(x=e.getBoundingRect()),p&&(v=b?_u(t,u,x):e.__canvasFillGradient,e.__canvasFillGradient=v),f&&(g=b?_u(t,c,x):e.__canvasStrokeGradient,e.__canvasStrokeGradient=g),d&&(_=b||!e.__canvasFillPattern?Mu(t,u,e):e.__canvasFillPattern,e.__canvasFillPattern=_),y&&(m=b||!e.__canvasStrokePattern?Mu(t,c,e):e.__canvasStrokePattern,e.__canvasStrokePattern=_),p?t.fillStyle=v:d&&(_?t.fillStyle=_:a=!1),f?t.strokeStyle=g:y&&(m?t.strokeStyle=m:o=!1));var S,k,T=e.getGlobalScale();w.setScale(T[0],T[1],e.segmentIgnoreThreshold),t.setLineDash&&r.lineDash&&(S=(n=wu(e))[0],k=n[1]);var C=!0;(l||b&Ue)&&(w.setDPR(t.dpr),h?w.setContext(null):(w.setContext(t),C=!1),w.reset(),e.buildPath(w,e.shape,i),w.toStatic(),e.pathUpdated()),C&&w.rebuildPath(t,h?s:1),S&&(t.setLineDash(S),t.lineDashOffset=k),i||(r.strokeFirst?(o&&Pu(t,r),a&&Cu(t,r)):(a&&Cu(t,r),o&&Pu(t,r))),S&&t.setLineDash([])}(t,e,p,a),a&&(r.batchFill=p.fill||"",r.batchStroke=p.stroke||"")):e instanceof _s?(r.lastDrawType!==Fu&&(H=!0,r.lastDrawType=Fu),zu(t,e,o,H,r),A=t,L=e,null!=(R=(D=p).text)&&(R+=""),R&&(A.font=D.font||E,A.textAlign=D.textAlign,A.textBaseline=D.textBaseline,O=I=void 0,A.setLineDash&&D.lineDash&&(I=(z=wu(L))[0],O=z[1]),I&&(A.setLineDash(I),A.lineDashOffset=O),D.strokeFirst?(Su(D)&&A.strokeText(R,D.x,D.y),Tu(D)&&A.fillText(R,D.x,D.y)):(Tu(D)&&A.fillText(R,D.x,D.y),Su(D)&&A.strokeText(R,D.x,D.y)),I&&A.setLineDash([]))):e instanceof La?(r.lastDrawType!==Ru&&(H=!0,r.lastDrawType=Ru),C=o,P=H,Du(t,Hu(e,(M=r).inHover),C&&Hu(C,M.inHover),P,M),f=t,y=p,(T=(d=e).__image=Bh(y.image,d.__image,d,d.onload))&&Hh(T)&&(v=y.x||0,g=y.y||0,_=d.getWidth(),m=d.getHeight(),x=T.width/T.height,null==_&&null!=m?_=m*x:null==m&&null!=_?m=_/x:null==_&&null==m&&(_=T.width,m=T.height),y.sWidth&&y.sHeight?(w=y.sx||0,b=y.sy||0,f.drawImage(T,w,b,y.sWidth,y.sHeight,v,g,_,m)):y.sx&&y.sy?(S=_-(w=y.sx),k=m-(b=y.sy),f.drawImage(T,w,b,S,k,v,g,_,m)):f.drawImage(T,v,g,_,m))):e.getTemporalDisplayables&&(r.lastDrawType!==Bu&&(H=!0,r.lastDrawType=Bu),function(t,e,r){var i=e.getDisplayables(),n=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){(u=i[o]).beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Eu(t,u,s,o===a-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}for(var h=0,l=n.length;h<l;h++){var u;(u=n[h]).beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Eu(t,u,s,h===l-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,r)),a&&i&&Nu(t,r),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(r.prevEl=e).__dirty=0,e.__isRendered=!0)}function Wu(t,e,r){var i=f.createCanvas(),n=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=n+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=n*r,i.height=o*r,i}var Xu,qu=(yt(Yu,Xu=Ft),Yu.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},Yu.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},Yu.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},Yu.prototype.setUnpainted=function(){this.__firstTimePaint=!0},Yu.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=Wu("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},Yu.prototype.createRepaintRects=function(t,e,r,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var c=[],p=this.maxRepaintRectCount,f=!1,d=new Se(0,0,0,0);function n(t){if(t.isFinite()&&!t.isZero())if(0===c.length)(e=new Se(0,0,0,0)).copy(t),c.push(e);else{for(var e,r=!1,i=1/0,n=0,o=0;o<c.length;++o){var a,s,h,l=c[o];if(l.intersect(t)){var u=new Se(0,0,0,0);u.copy(l),u.union(t),c[o]=u,r=!0;break}f&&(d.copy(t),d.union(l),a=t.width*t.height,s=l.width*l.height,(h=d.width*d.height-a-s)<i&&(i=h,n=o))}f&&(c[n].union(t),r=!0),r||((e=new Se(0,0,0,0)).copy(t),c.push(e)),f=f||c.length>=p}}for(var o,a=this.__startIndex;a<this.__endIndex;++a)(h=t[a])&&(u=h.shouldBePainted(r,i,!0,!0),(l=h.__isRendered&&(h.__dirty&Ve||!u)?h.getPrevPaintRect():null)&&n(l),(o=u&&(h.__dirty&Ve||!h.__isRendered)?h.getPaintRect():null)&&n(o));for(var s,a=this.__prevStartIndex;a<this.__prevEndIndex;++a){var h,l,u=(h=e[a])&&h.shouldBePainted(r,i,!0,!0);!h||u&&h.__zr||!h.__isRendered||(l=h.getPrevPaintRect())&&n(l)}do{for(s=!1,a=0;a<c.length;)if(c[a].isZero())c.splice(a,1);else{for(var y=a+1;y<c.length;)c[a].intersect(c[y])?(s=!0,c[a].union(c[y]),c.splice(y,1)):y++;a++}}while(s);return this._paintRects=c},Yu.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},Yu.prototype.resize=function(t,e){var r=this.dpr,i=this.dom,n=i.style,o=this.domBack;n&&(n.width=t+"px",n.height=e+"px"),i.width=t*r,i.height=e*r,o&&(o.width=t*r,o.height=e*r,1!==r&&this.ctxBack.scale(r,r))},Yu.prototype.clear=function(t,o,e){var r=this.dom,a=this.ctx,i=r.width,n=r.height;o=o||this.clearColor;var s=this.motionBlur&&!t,h=this.lastFrameAlpha,l=this.dpr,u=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(r,0,0,i/l,n/l));var c=this.domBack;function p(t,e,r,i){var n;a.clearRect(t,e,r,i),o&&"transparent"!==o&&(n=void 0,j(o)?(n=(o.global||o.__width===r&&o.__height===i)&&o.__canvasGradient||_u(a,o,{x:0,y:0,width:r,height:i}),o.__canvasGradient=n,o.__width=r,o.__height=i):V(o)&&(o.scaleX=o.scaleX||l,o.scaleY=o.scaleY||l,n=Mu(a,o,{dirty:function(){u.setUnpainted(),u.painter.refresh()}})),a.save(),a.fillStyle=n||o,a.fillRect(t,e,r,i),a.restore()),s&&(a.save(),a.globalAlpha=h,a.drawImage(c,t,e,r,i),a.restore())}!e||s?p(0,0,i,n):e.length&&O(e,function(t){p(t.x*l,t.y*l,t.width*l,t.height*l)})},Yu);function Yu(t,e,r){var i,n=Xu.call(this)||this;n.motionBlur=!1,n.lastFrameAlpha=.7,n.dpr=1,n.virtual=!1,n.config={},n.incremental=!1,n.zlevel=0,n.maxRepaintRectCount=5,n.__dirty=!0,n.__firstTimePaint=!0,n.__used=!1,n.__drawIndex=0,n.__startIndex=0,n.__endIndex=0,n.__prevStartIndex=null,n.__prevEndIndex=null,r=r||rn,"string"==typeof t?i=Wu(t,e,r):W(t)&&(t=(i=t).id),n.id=t;var o=(n.dom=i).style;return o&&(lt(i),i.onselectstart=function(){return!1},o.padding="0",o.margin="0",o.borderWidth="0"),n.painter=e,n.dpr=r,n}var ju=314159;var Vu=(Uu.prototype.getType=function(){return"canvas"},Uu.prototype.isSingleCanvas=function(){return this._singleCanvas},Uu.prototype.getViewportRoot=function(){return this._domRoot},Uu.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},Uu.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),r=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,r,t,this._redrawId);for(var n=0;n<i.length;n++){var o,a=i[n],s=this._layers[a];!s.__builtin__&&s.refresh&&(o=0===n?this._backgroundColor:null,s.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},Uu.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},Uu.prototype._paintHoverList=function(t){var e=t.length,r=this._hoverlayer;if(r&&r.clear(),e){for(var i,n={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(r=r||(this._hoverlayer=this.getLayer(1e5)),i||(i=r.ctx).save(),Eu(i,a,n,o===e-1))}i&&i.restore()}},Uu.prototype.getHoverLayer=function(){return this.getLayer(1e5)},Uu.prototype.paintOne=function(t,e){Eu(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)},Uu.prototype._paintList=function(t,e,r,i){var n,o,a,s;this._redrawId===i&&(r=r||!1,this._updateLayerStatus(t),o=(n=this._doPaintList(t,e,r)).finished,a=n.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o?this.eachLayer(function(t){t.afterBrush&&t.afterBrush()}):(s=this,Je(function(){s._paintList(t,e,r,i)})))},Uu.prototype._compositeManually=function(){var e=this.getLayer(ju).ctx,r=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,r,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,r,i)})},Uu.prototype._doPaintList=function(d,y,v){for(var g=this,_=[],m=this._opts.useDirtyRect,t=0;t<this._zlevelList.length;t++){var e=this._zlevelList[t],r=this._layers[e];r.__builtin__&&r!==this._hoverlayer&&(r.__dirty||v)&&_.push(r)}for(var x=!0,w=!1,b=this,i=0;i<_.length;i++)!function(t){var e,i,n=_[t],o=n.ctx,r=m&&n.createRepaintRects(d,y,b._width,b._height),a=v?n.__startIndex:n.__drawIndex,s=!v&&n.incremental&&Date.now,h=s&&Date.now(),l=n.zlevel===b._zlevelList[0]?b._backgroundColor:null;function u(t){var e={inHover:!1,allClipped:!1,prevEl:null,viewWidth:g._width,viewHeight:g._height};for(i=a;i<n.__endIndex;i++){var r=d[i];if(r.__inHover&&(w=!0),g._doPaintEl(r,n,m,t,e,i===n.__endIndex-1),s&&15<Date.now()-h)break}e.prevElClipPaths&&o.restore()}if(n.__startIndex===n.__endIndex?n.clear(!1,l,r):a===n.__startIndex&&((e=d[a]).incremental&&e.notClear&&!v||n.clear(!1,l,r)),-1===a&&(console.error("For some unknown reason. drawIndex is -1"),a=n.__startIndex),r)if(0===r.length)i=n.__endIndex;else for(var c=b.dpr,p=0;p<r.length;++p){var f=r[p];o.save(),o.beginPath(),o.rect(f.x*c,f.y*c,f.width*c,f.height*c),o.clip(),u(f),o.restore()}else o.save(),u(),o.restore();n.__drawIndex=i,n.__drawIndex<n.__endIndex&&(x=!1)}(i);return c.wxa&&O(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:x,needsRefreshHover:w}},Uu.prototype._doPaintEl=function(t,e,r,i,n,o){var a,s=e.ctx;r?(a=t.getPaintRect(),(!i||a&&a.intersect(i))&&(Eu(s,t,n,o),t.setPrevPaintRect(a))):Eu(s,t,n,o)},Uu.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=ju);var r=this._layers[t];return r||((r=new qu("zr_"+t,this,this.dpr)).zlevel=t,r.__builtin__=!0,this._layerConfig[t]?S(r,this._layerConfig[t],!0):this._layerConfig[t-.01]&&S(r,this._layerConfig[t-.01],!0),e&&(r.virtual=e),this.insertLayer(t,r),r.initContext()),r},Uu.prototype.insertLayer=function(t,e){var r,i=this._layers,n=this._zlevelList,o=n.length,a=this._domRoot,s=null,h=-1;if(!i[t]&&function(t){if(t){if(t.__builtin__)return 1;if("function"==typeof t.resize&&"function"==typeof t.refresh)return 1}}(e)){if(0<o&&t>n[0]){for(h=0;h<o-1&&!(n[h]<t&&n[h+1]>t);h++);s=i[n[h]]}n.splice(h+1,0,t),(i[t]=e).virtual||(s?(r=s.dom).nextSibling?a.insertBefore(e.dom,r.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.painter||(e.painter=this)}},Uu.prototype.eachLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i];t.call(e,this._layers[n],n)}},Uu.prototype.eachBuiltinLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__&&t.call(e,o,n)}},Uu.prototype.eachOtherLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__||t.call(e,o,n)}},Uu.prototype.getLayers=function(){return this._layers},Uu.prototype._updateLayerStatus=function(t){function e(t){n&&(n.__endIndex!==t&&(n.__dirty=!0),n.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var r=1;r<t.length;r++)if((s=t[r]).zlevel!==t[r-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,n=null,o=0,a=0;a<t.length;a++){var s,h=(s=t[a]).zlevel,l=void 0;i!==h&&(i=h,o=0),s.incremental?((l=this.getLayer(h+.001,this._needsManuallyCompositing)).incremental=!0,o=1):l=this.getLayer(h+(0<o?.01:0),this._needsManuallyCompositing),l.__builtin__||b("ZLevel "+h+" has been used by unkown layer "+l.id),l!==n&&(l.__used=!0,l.__startIndex!==a&&(l.__dirty=!0),l.__startIndex=a,l.incremental?l.__drawIndex=-1:l.__drawIndex=a,e(a),n=l),s.__dirty&Ve&&!s.__inHover&&(l.__dirty=!0,l.incremental&&l.__drawIndex<0&&(l.__drawIndex=a))}e(a),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},Uu.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},Uu.prototype._clearLayer=function(t){t.clear()},Uu.prototype.setBackgroundColor=function(t){this._backgroundColor=t,O(this._layers,function(t){t.setUnpainted()})},Uu.prototype.configLayer=function(t,e){if(e){var r=this._layerConfig;r[t]?S(r[t],e,!0):r[t]=e;for(var i=0;i<this._zlevelList.length;i++){var n=this._zlevelList[i];n!==t&&n!==t+.01||S(this._layers[n],r[t],!0)}}},Uu.prototype.delLayer=function(t){var e=this._layers,r=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],r.splice(P(r,t),1))},Uu.prototype.resize=function(t,e){if(this._domRoot.style){var r=this._domRoot;r.style.display="none";var i=this._opts,n=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=xu(n,0,i),e=xu(n,1,i),r.style.display="",this._width!==t||e!==this._height){for(var o in r.style.width=t+"px",r.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(ju).resize(t,e)}return this},Uu.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},Uu.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},Uu.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[ju].dom;var e=new qu("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var r=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(r.save(),t.renderToCanvas(r),r.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,h=a.length;s<h;s++){var l=a[s];Eu(r,l,o,s===h-1)}return e.dom},Uu.prototype.getWidth=function(){return this._width},Uu.prototype.getHeight=function(){return this._height},Uu);function Uu(t,e,r,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=r=I({},r||{}),this.dpr=r.devicePixelRatio||rn,this._singleCanvas=n,(this.root=t).style&&(lt(t),t.innerHTML=""),this.storage=e;var o=this._zlevelList;this._prevDisplayList=[];var a,s,h,l,u,c,p,f,d=this._layers;n?(s=(a=t).width,h=a.height,null!=r.width&&(s=r.width),null!=r.height&&(h=r.height),this.dpr=r.devicePixelRatio||1,a.width=s*this.dpr,a.height=h*this.dpr,this._width=s,this._height=h,(l=new qu(a,this,this.dpr)).__builtin__=!0,l.initContext(),(d[ju]=l).zlevel=ju,o.push(ju),this._domRoot=t):(this._width=xu(t,0,r),this._height=xu(t,1,r),u=this._domRoot=(c=this._width,p=this._height,(f=document.createElement("div")).style.cssText=["position:relative","width:"+c+"px","height:"+p+"px","padding:0","margin:0","border-width:0"].join(";")+";",f),t.appendChild(u))}var Gu=Math.sin,Zu=Math.cos,Ku=Math.PI,Qu=2*Math.PI,$u=180/Ku,Ju=(tc.prototype.reset=function(t){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,t||4)},tc.prototype.moveTo=function(t,e){this._add("M",t,e)},tc.prototype.lineTo=function(t,e){this._add("L",t,e)},tc.prototype.bezierCurveTo=function(t,e,r,i,n,o){this._add("C",t,e,r,i,n,o)},tc.prototype.quadraticCurveTo=function(t,e,r,i){this._add("Q",t,e,r,i)},tc.prototype.arc=function(t,e,r,i,n,o){this.ellipse(t,e,r,r,0,i,n,o)},tc.prototype.ellipse=function(t,e,r,i,n,o,a,s){var h=a-o,l=!s,u=Math.abs(h),c=ii(u-Qu)||(l?Qu<=h:Qu<=-h),p=!1,p=!!c||!ii(u)&&Ku<=(0<h?h%Qu:h%Qu+Qu)==!!l,f=t+r*Zu(o),d=e+i*Gu(o);this._start&&this._add("M",f,d);var y,v,g,_,m=Math.round(n*$u);c?(y=1/this._p,v=(l?1:-1)*(Qu-y),this._add("A",r,i,m,1,+l,t+r*Zu(o+v),e+i*Gu(o+v)),.01<y&&this._add("A",r,i,m,0,+l,f,d)):(g=t+r*Zu(a),_=e+i*Gu(a),this._add("A",r,i,m,+p,+l,g,_))},tc.prototype.rect=function(t,e,r,i){this._add("M",t,e),this._add("l",r,0),this._add("l",0,i),this._add("l",-r,0),this._add("Z")},tc.prototype.closePath=function(){0<this._d.length&&this._add("Z")},tc.prototype._add=function(t,e,r,i,n,o,a,s,h){for(var l=[],u=this._p,c=1;c<arguments.length;c++){var p=arguments[c];if(isNaN(p))return void(this._invalid=!0);l.push(Math.round(p*u)/u)}this._d.push(t+l.join(" ")),this._start="Z"===t},tc.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},tc.prototype.getStr=function(){return this._str},tc);function tc(){}var ec="none",rc=Math.round;var ic=["lineCap","miterLimit","lineJoin"],nc=L(ic,function(t){return"stroke-"+t.toLowerCase()});function oc(t,e,r,i){var n,o,a,s,h=null==e.opacity?1:e.opacity;if(r instanceof La)t("opacity",h);else if(null!=(a=e.fill)&&a!==ec?(t("fill",(n=ei(e.fill)).color),o=null!=e.fillOpacity?e.fillOpacity*n.opacity*h:n.opacity*h,(i||o<1)&&t("fill-opacity",o)):t("fill",ec),null!=(s=e.stroke)&&s!==ec){var l=ei(e.stroke);t("stroke",l.color);var u,c,p,f=e.strokeNoScale?r.getLineScale():1,d=f?(e.lineWidth||0)/f:0,y=null!=e.strokeOpacity?e.strokeOpacity*l.opacity*h:l.opacity*h,v=e.strokeFirst;!i&&1==d||t("stroke-width",d),(i||v)&&t("paint-order",v?"stroke":"fill"),(i||y<1)&&t("stroke-opacity",y),e.lineDash?(c=(u=wu(r))[0],p=u[1],c&&(p=rc(p||0),t("stroke-dasharray",c.join(",")),(p||i)&&t("stroke-dashoffset",p))):i&&t("stroke-dasharray",ec);for(var g=0;g<ic.length;g++){var _,m=ic[g];!i&&e[m]===Jo[m]||(_=e[m]||Jo[m])&&t(nc[g],_)}}else i&&t("stroke",ec)}var ac="http://www.w3.org/2000/svg",sc="http://www.w3.org/1999/xlink",hc="http://www.w3.org/2000/xmlns/",lc="http://www.w3.org/XML/1998/namespace",uc="ecmeta_";function cc(t){return document.createElementNS(ac,t)}function pc(t,e,r,i,n){return{tag:t,attrs:r||{},children:i,text:n,key:e}}function fc(t,e){var s=(e=e||{}).newline?"\n":"";return function t(e){var r,i=e.children,n=e.tag,o=e.attrs,a=e.text;return function(t,e){var r=[];if(e)for(var i in e){var n=e[i],o=i;!1!==n&&(!0!==n&&null!=n&&(o+='="'+n+'"'),r.push(o))}return"<"+t+" "+r.join(" ")+">"}(n,o)+("style"!==n?null==(r=a)?"":(r+"").replace(Yt,function(t,e){return jt[e]}):a||"")+(i?""+s+L(i,t).join(s)+s:"")+"</"+n+">"}(t)}function dc(t){return{zrId:t,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function yc(t,e,r,i){return pc("svg","root",{width:t,height:e,xmlns:ac,"xmlns:xlink":sc,version:"1.1",baseProfile:"full",viewBox:!!i&&"0 0 "+t+" "+e},r)}var vc=0;function gc(){return vc++}var _c={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},mc="transform-origin";var xc={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function wc(t,e){var r=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[r]=t,r}function bc(t){return N(t)?_c[t]?"cubic-bezier("+_c[t]+")":br(t)?t:"":""}function Sc(A,L,D,z){var t=A.animators,e=t.length,r=[];if(A instanceof Lh){if(i=function(t,e,c){var p,f,r=t.shape.paths,d={};if(O(r,function(t){var e=dc(c.zrId);e.animation=!0,Sc(t,{},e,!0);var r=e.cssAnims,i=e.cssNodes,n=F(r),o=n.length;if(o){var a=r[f=n[o-1]];for(var s in a){var h=a[s];d[s]=d[s]||{d:""},d[s].d+=h.d||""}for(var l in i){var u=i[l].animation;0<=u.indexOf(f)&&(p=u)}}}),p){e.d=!1;var i=wc(d,c);return p.replace(f,i)}}(A,L,D))r.push(i);else if(!e)return}else if(!e)return;for(var i,n,o={},a=0;a<e;a++){var s=t[a],h=[s.getMaxTime()/1e3+"s"],l=bc(s.getClip().easing),u=s.getDelay();l?h.push(l):h.push("linear"),u&&h.push(u/1e3+"s"),s.getLoop()&&h.push("infinite");var c=h.join(" ");o[c]=o[c]||[c,[]],o[c][1].push(s)}function p(t){var e=t[1],r=e.length,i={},n={},o={},d="animation-timing-function";function a(t,e,r){for(var i=t.getTracks(),n=t.getMaxTime(),o=0;o<i.length;o++){var a=i[o];if(a.needsAnimate()){var s=a.keyframes,h=a.propName;if(r&&(h=r(h)),h)for(var l=0;l<s.length;l++){var u=s[l],c=Math.round(u.time/n*100)+"%",p=bc(u.easing),f=u.rawValue;(N(f)||H(f))&&(e[c]=e[c]||{},e[c][h]=u.rawValue,p&&(e[c][d]=p))}}}}for(var s,h,l,u,c,p=0;p<r;p++){(S=(b=e[p]).targetName)?"shape"===S&&a(b,n):z||a(b,i)}for(var f in i){var y={};vn(y,A),I(y,i[f]);var v=di(y),g=i[f][d];o[f]=v?{transform:v}:{},s=o[f],u=l=void 0,l=(h=y).originX,u=h.originY,(l||u)&&(s[mc]=l+"px "+u+"px"),g&&(o[f][d]=g)}var _=!0;for(var f in n){o[f]=o[f]||{};var m=!c,g=n[f][d];m&&(c=new Oo);var x=c.len();c.reset(),o[f].d=function(t,e,r){var i=I({},t.shape);I(i,e),t.buildPath(r,i);var n=new Ju;return n.reset(fi(t)),r.rebuildPath(n,1),n.generateStr(),n.getStr()}(A,n[f],c);var w=c.len();if(!m&&x!==w){_=!1;break}g&&(o[f][d]=g)}if(!_)for(var f in o)delete o[f].d;if(!z)for(var b,S,p=0;p<r;p++){"style"===(S=(b=e[p]).targetName)&&a(b,o,function(t){return xc[t]})}for(var k,T=F(o),C=!0,p=1;p<T.length;p++){var P=T[p-1],M=T[p];if(o[P][mc]!==o[M][mc]){C=!1;break}k=o[P][mc]}if(C&&k){for(var f in o)o[f][mc]&&delete o[f][mc];L[mc]=k}if(R(T,function(t){return 0<F(o[t]).length}).length)return wc(o,D)+" "+t[0]+" both"}for(var f in o){(i=p(o[f]))&&r.push(i)}r.length&&(n=D.zrId+"-cls-"+gc(),D.cssNodes["."+n]={animation:r.join(",")},L.class=n)}function kc(t,e,r,i){var n=JSON.stringify(t),o=r.cssStyleCache[n];o||(o=r.zrId+"-cls-"+gc(),r.cssStyleCache[n]=o,r.cssNodes["."+o+(i?":hover":"")]=t),e.class=e.class?e.class+" "+o:o}var Tc=Math.round;function Cc(t){return t&&N(t.src)}function Pc(t){return t&&B(t.toDataURL)}function Mc(i,n,o,a){oc(function(t,e){var r="fill"===t||"stroke"===t;r&&ci(e)?Nc(n,i,t,a):r&&hi(e)?Hc(o,i,t,a):i[t]=e,r&&a.ssr&&"none"===e&&(i["pointer-events"]="visible")},n,o,!1),function(t,e,r){var i=t.style;if(function(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}(i)){var n=function(t){var e=t.style,r=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),r[0],r[1]].join(",")}(t),o=r.shadowCache,a=o[n];if(!a){var s=t.getGlobalScale(),h=s[0],l=s[1];if(!h||!l)return;var u=i.shadowOffsetX||0,c=i.shadowOffsetY||0,p=i.shadowBlur,f=ei(i.shadowColor),d=f.opacity,y=f.color,v=p/2/h+" "+p/2/l;a=r.zrId+"-s"+r.shadowIdx++,r.defs[a]=pc("filter",a,{id:a,x:"-100%",y:"-100%",width:"300%",height:"300%"},[pc("feDropShadow","",{dx:u/h,dy:c/l,stdDeviation:v,"flood-color":y,"flood-opacity":d})]),o[n]=a}e.filter=pi(a)}}(o,i,a)}function Ac(r,t){var e=jn(t);e&&(e.each(function(t,e){null!=t&&(r[(uc+e).toLowerCase()]=t+"")}),t.isSilent()&&(r[uc+"silent"]="true"))}function Lc(t){return ii(t[0]-1)&&ii(t[1])&&ii(t[2])&&ii(t[3]-1)}function Dc(t,e,r){var i,n,o;!e||ii((o=e)[4])&&ii(o[5])&&Lc(e)||(i=r?10:1e4,t.transform=Lc(e)?"translate("+Tc(e[4]*i)/i+" "+Tc(e[5]*i)/i+")":"matrix("+ni((n=e)[0])+","+ni(n[1])+","+ni(n[2])+","+ni(n[3])+","+oi(n[4])+","+oi(n[5])+")")}function zc(t,e,r){for(var i=t.points,n=[],o=0;o<i.length;o++)n.push(Tc(i[o][0]*r)/r),n.push(Tc(i[o][1]*r)/r);e.points=n.join(" ")}function Ic(t){return!t.smooth}var Oc,Rc={circle:[(Oc=L(["cx","cy","r"],function(t){return"string"==typeof t?[t,t]:t}),function(t,e,r){for(var i=0;i<Oc.length;i++){var n=Oc[i],o=t[n[0]];null!=o&&(e[n[1]]=Tc(o*r)/r)}})],polyline:[zc,Ic],polygon:[zc,Ic]};function Fc(t,e){var r,i,n,o,a,s,h,l,u,c,p,f,d,y,v,g,_=t.style,m=t.shape,x=Rc[t.type],w={},b=e.animation,S="path",k=t.style.strokePercent,T=e.compress&&fi(t)||4;return!x||e.willUpdate||x[1]&&!x[1](m)||b&&function(t){for(var e=t.animators,r=0;r<e.length;r++)if("shape"===e[r].targetName)return 1}(t)||k<1?(r=!t.path||t.shapeChanged(),t.path||t.createPathProxy(),i=t.path,r&&(i.beginPath(),t.buildPath(i,t.shape),t.pathUpdated()),n=i.getVersion(),a=(o=t).__svgPathBuilder,o.__svgPathVersion===n&&a&&k===o.__svgPathStrokePercent||((a=a||(o.__svgPathBuilder=new Ju)).reset(T),i.rebuildPath(a,k),a.generateStr(),o.__svgPathVersion=n,o.__svgPathStrokePercent=k),w.d=a.getStr()):(S=t.type,s=Math.pow(10,T),x[0](m,w,s)),Dc(w,t.transform),Mc(w,_,t,e),Ac(w,t),e.animation&&Sc(t,w,e),e.emphasis&&(l=w,u=e,(h=t).ignore||(h.isSilent()?kc(g={"pointer-events":"none"},l,u,!0):((p=(c=h.states.emphasis&&h.states.emphasis.style?h.states.emphasis.style:{}).fill)||(f=h.style&&h.style.fill,d=h.states.select&&h.states.select.style&&h.states.select.style.fill,(y=0<=h.currentStates.indexOf("select")&&d||f)&&(p=$r(y))),(v=c.lineWidth)&&(v/=!c.strokeNoScale&&h.transform?h.transform[0]:1),g={cursor:"pointer"},p&&(g.fill=p),c.stroke&&(g.stroke=c.stroke),v&&(g["stroke-width"]=v),kc(g,l,u,!0)))),pc(S,t.id+"",w)}function Bc(t,e){return t instanceof ra?Fc(t,e):t instanceof La?function(t,e){var r=t.style,i=r.image;if(i&&!N(i)&&(Cc(i)?i=i.src:Pc(i)&&(i=i.toDataURL())),i){var n=r.x||0,o=r.y||0,a={href:i,width:r.width,height:r.height};return n&&(a.x=n),o&&(a.y=o),Dc(a,t.transform),Mc(a,r,t,e),Ac(a,t),e.animation&&Sc(t,a,e),pc("image",t.id+"",a)}}(t,e):t instanceof _s?function(t,e){var r=t.style,i=r.text;if(null!=i&&(i+=""),i&&!isNaN(r.x)&&!isNaN(r.y)){var n,o,a,s=r.font||E,h=r.x||0,l=(n=r.y||0,o=bn(s),"top"===(a=r.textBaseline)?n+=o/2:"bottom"===a&&(n-=o/2),n),u={"dominant-baseline":"central","text-anchor":ai[r.textAlign]||r.textAlign};if(al(r)){var c="",p=r.fontStyle,f=nl(r.fontSize);if(!parseFloat(f))return;var d=r.fontFamily||v,y=r.fontWeight;c+="font-size:"+f+";font-family:"+d+";",p&&"normal"!==p&&(c+="font-style:"+p+";"),y&&"normal"!==y&&(c+="font-weight:"+y+";"),u.style=c}else u.style="font: "+s;return i.match(/\s/)&&(u["xml:space"]="preserve"),h&&(u.x=h),l&&(u.y=l),Dc(u,t.transform),Mc(u,r,t,e),Ac(u,t),e.animation&&Sc(t,u,e),pc("text",t.id+"",u,void 0,i)}}(t,e):void 0}function Nc(t,e,r,i){var n,o=t[r],a={gradientUnits:o.global?"userSpaceOnUse":"objectBoundingBox"};if(li(o))n="linearGradient",a.x1=o.x,a.y1=o.y,a.x2=o.x2,a.y2=o.y2;else{if(!ui(o))return;n="radialGradient",a.cx=Z(o.x,.5),a.cy=Z(o.y,.5),a.r=Z(o.r,.5)}for(var s=o.colorStops,h=[],l=0,u=s.length;l<u;++l){var c=100*oi(s[l].offset)+"%",p=ei(s[l].color),f=p.color,d=p.opacity,y={offset:c};y["stop-color"]=f,d<1&&(y["stop-opacity"]=d),h.push(pc("stop",l+"",y))}var v=fc(pc(n,"",a,h)),g=i.gradientCache,_=g[v];_||(_=i.zrId+"-g"+i.gradientIdx++,g[v]=_,a.id=_,i.defs[_]=pc(n,_,a,h)),e[r]=pi(_)}function Hc(t,e,r,i){var n,o,a,s,h,l,u,c,p,f,d,y,v,g,_,m=t.style[r],x=t.getBoundingRect(),w={},b=m.repeat,S="no-repeat"===b,k="repeat-x"===b,T="repeat-y"===b;si(m)?(o=m.imageWidth,a=m.imageHeight,s=void 0,N(h=m.image)?s=h:Cc(h)?s=h.src:Pc(h)&&(s=h.toDataURL()),"undefined"==typeof Image?(J(o,l="Image width/height must been given explictly in svg-ssr renderer."),J(a,l)):null!=o&&null!=a||(u=function(t,e){var r,i,n;t&&(r=t.elm,i=o||e.width,n=a||e.height,"pattern"===t.tag&&(k?(n=1,i/=x.width):T&&(i=1,n/=x.height)),t.attrs.width=i,t.attrs.height=n,r&&(r.setAttribute("width",i),r.setAttribute("height",n)))},(c=Bh(s,null,t,function(t){S||u(y,t),u(n,t)}))&&c.width&&c.height&&(o=o||c.width,a=a||c.height)),n=pc("image","img",{href:s,width:o,height:a}),w.width=o,w.height=a):m.svgElement&&(n=C(m.svgElement),w.width=m.svgWidth,w.height=m.svgHeight),n&&(S?p=f=1:k?(f=1,p=w.width/x.width):T?(p=1,f=w.height/x.height):w.patternUnits="userSpaceOnUse",null==p||isNaN(p)||(w.width=p),null==f||isNaN(f)||(w.height=f),(d=di(m))&&(w.patternTransform=d),v=fc(y=pc("pattern","",w,[n])),(_=(g=i.patternCache)[v])||(_=i.zrId+"-p"+i.patternIdx++,g[v]=_,w.id=_,y=i.defs[_]=pc("pattern",_,w,[n])),e[r]=pi(_))}function Ec(t){return document.createTextNode(t)}function Wc(t,e,r){t.insertBefore(e,r)}function Xc(t,e){t.removeChild(e)}function qc(t,e){t.appendChild(e)}function Yc(t){return t.parentNode}function jc(t){return t.nextSibling}function Vc(t,e){t.textContent=e}var Uc=58,Gc=120,Zc=pc("","");function Kc(t){return void 0===t}function Qc(t){return void 0!==t}function $c(t,e){var r=t.key===e.key;return t.tag===e.tag&&r}function Jc(t){var e,r=t.children,i=t.tag;if(Qc(i)){var n=t.elm=cc(i);if(rp(Zc,t),G(r))for(e=0;e<r.length;++e){var o=r[e];null!=o&&qc(n,Jc(o))}else Qc(t.text)&&!W(t.text)&&qc(n,Ec(t.text))}else t.elm=Ec(t.text);return t.elm}function tp(t,e,r,i,n){for(;i<=n;++i){var o=r[i];null!=o&&Wc(t,Jc(o),e)}}function ep(t,e,r,i){for(;r<=i;++r){var n=e[r];null!=n&&(Qc(n.tag)?Xc(Yc(n.elm),n.elm):Xc(t,n.elm))}}function rp(t,e){var r,i=e.elm,n=t&&t.attrs||{},o=e.attrs||{};if(n!==o){for(r in o){var a=o[r];n[r]!==a&&(!0===a?i.setAttribute(r,""):!1===a?i.removeAttribute(r):"style"===r?i.style.cssText=a:r.charCodeAt(0)!==Gc?i.setAttribute(r,a):"xmlns:xlink"===r||"xmlns"===r?i.setAttributeNS(hc,r,a):r.charCodeAt(3)===Uc?i.setAttributeNS(lc,r,a):r.charCodeAt(5)===Uc?i.setAttributeNS(sc,r,a):i.setAttribute(r,a))}for(r in n)r in o||i.removeAttribute(r)}}function ip(t,e,r){for(var i,n,o,a=0,s=0,h=e.length-1,l=e[0],u=e[h],c=r.length-1,p=r[0],f=r[c];a<=h&&s<=c;)null==l?l=e[++a]:null==u?u=e[--h]:null==p?p=r[++s]:null==f?f=r[--c]:$c(l,p)?(np(l,p),l=e[++a],p=r[++s]):$c(u,f)?(np(u,f),u=e[--h],f=r[--c]):$c(l,f)?(np(l,f),Wc(t,l.elm,jc(u.elm)),l=e[++a],f=r[--c]):p=($c(u,p)?(np(u,p),Wc(t,u.elm,l.elm),u=e[--h]):(Kc(i)&&(i=function(t,e,r){for(var i={},n=e;n<=r;++n){var o=t[n].key;void 0!==o&&(i[o]=n)}return i}(e,a,h)),Kc(n=i[p.key])||(o=e[n]).tag!==p.tag?Wc(t,Jc(p),l.elm):(np(o,p),e[n]=void 0,Wc(t,o.elm,l.elm))),r[++s]);(a<=h||s<=c)&&(h<a?tp(t,null==r[c+1]?null:r[c+1].elm,r,s,c):ep(t,e,a,h))}function np(t,e){var r=e.elm=t.elm,i=t.children,n=e.children;t!==e&&(rp(t,e),Kc(e.text)?Qc(i)&&Qc(n)?i!==n&&ip(r,i,n):Qc(n)?(Qc(t.text)&&Vc(r,""),tp(r,null,n,0,n.length-1)):Qc(i)?ep(r,i,0,i.length-1):Qc(t.text)&&Vc(r,""):t.text!==e.text&&(Qc(i)&&ep(r,i,0,i.length-1),Vc(r,e.text)))}var op=0,ap=(sp.prototype.getType=function(){return this.type},sp.prototype.getViewportRoot=function(){return this._viewport},sp.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},sp.prototype.getSvgDom=function(){return this._svgDom},sp.prototype.refresh=function(){var t,e,r,i,n;this.root&&((t=this.renderToVNode({willUpdate:!0})).attrs.style="position:absolute;left:0;top:0;user-select:none",$c(e=this._oldVNode,r=t)?np(e,r):(n=Yc(i=e.elm),Jc(r),null!==n&&(Wc(n,r.elm,jc(i)),ep(n,[e],0,0))),this._oldVNode=t)},sp.prototype.renderOneToVNode=function(t){return Bc(t,dc(this._id))},sp.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),r=this._width,i=this._height,n=dc(this._id);n.animation=t.animation,n.willUpdate=t.willUpdate,n.compress=t.compress,n.emphasis=t.emphasis,n.ssr=this._opts.ssr;var o=[],a=this._bgVNode=function(t,e,r,i){var n,o,a,s;return r&&"none"!==r&&(n=pc("rect","bg",{width:t,height:e,x:"0",y:"0"}),ci(r)?Nc({fill:r},n.attrs,"fill",i):hi(r)?Hc({style:{fill:r},dirty:ct,getBoundingRect:function(){return{width:t,height:e}}},n.attrs,"fill",i):(o=ei(r),a=o.color,s=o.opacity,n.attrs.fill=a,s<1&&(n.attrs["fill-opacity"]=s))),n}(r,i,this._backgroundColor,n);a&&o.push(a);var s=t.compress?null:this._mainVNode=pc("g","main",{},[]);this._paintList(e,n,s?s.children:o),s&&o.push(s);var h,l,u,c,p,f,d,y,v,g=L(F(n.defs),function(t){return n.defs[t]});return g.length&&o.push(pc("defs","defs",{},g)),t.animation&&(u=n.cssNodes,c=n.cssAnims,f=" {"+(p="\n"),d=p+"}",y=L(F(u),function(e){return e+f+L(F(u[e]),function(t){return t+":"+u[e][t]+";"}).join(p)+d}).join(p),v=L(F(c),function(i){return"@keyframes "+i+f+L(F(c[i]),function(r){return r+f+L(F(c[i][r]),function(t){var e=c[i][r][t];return"d"===t&&(e='path("'+e+'")'),t+":"+e+";"}).join(p)+d}).join(p)+d}).join(p),(h=y||v?["<![CDATA[",y,v,"]]>"].join(p):"")&&(l=pc("style","stl",{},[],h),o.push(l))),yc(r,i,o,t.useViewBox)},sp.prototype.renderToString=function(t){return t=t||{},fc(this.renderToVNode({animation:Z(t.cssAnimation,!0),emphasis:Z(t.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:Z(t.useViewBox,!0)}),{newline:!0})},sp.prototype.setBackgroundColor=function(t){this._backgroundColor=t},sp.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},sp.prototype._paintList=function(t,e,r){for(var i,n,o,a,s,h,l,u,c,p=t.length,f=[],d=0,y=0,v=0;v<p;v++){var g=t[v];if(!g.invisible){for(var _=g.__clipPaths,m=_&&_.length||0,x=n&&n.length||0,w=void 0,w=Math.max(m-1,x-1);0<=w&&(!_||!n||_[w]!==n[w]);w--);for(var b=x-1;w<b;b--)i=f[--d-1];for(var S=w+1;S<m;S++){var k={};o=_[S],a=k,c=u=l=h=void 0,l=(s=e).clipPathCache,u=s.defs,(c=l[o.id])||(h={id:c=s.zrId+"-c"+s.clipPathIdx++},u[l[o.id]=c]=pc("clipPath",c,h,[Fc(o,s)])),a["clip-path"]=pi(c);var T=pc("g","clip-g-"+y++,k,[]);(i?i.children:r).push(T),i=f[d++]=T}n=_;var C=Bc(g,e);C&&(i?i.children:r).push(C)}}},sp.prototype.resize=function(t,e){var r,i,n,o=this._opts,a=this.root,s=this._viewport;null!=t&&(o.width=t),null!=e&&(o.height=e),a&&s&&(s.style.display="none",t=xu(a,0,o),e=xu(a,1,o),s.style.display=""),this._width===t&&this._height===e||(this._width=t,this._height=e,s&&((r=s.style).width=t+"px",r.height=e+"px"),hi(this._backgroundColor)?this.refresh():((i=this._svgDom)&&(i.setAttribute("width",t),i.setAttribute("height",e)),(n=this._bgVNode&&this._bgVNode.elm)&&(n.setAttribute("width",t),n.setAttribute("height",e))))},sp.prototype.getWidth=function(){return this._width},sp.prototype.getHeight=function(){return this._height},sp.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},sp.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},sp.prototype.toDataURL=function(t){var e=this.renderToString(),r="data:image/svg+xml;";return t?(e=yi(e))&&r+"base64,"+e:r+"charset=UTF-8,"+encodeURIComponent(e)},sp);function sp(t,e,r){var i,n;this.type="svg",this.refreshHover=hp(),this.configLayer=hp(),this.storage=e,this._opts=r=I({},r),this.root=t,this._id="zr"+op++,this._oldVNode=yc(r.width,r.height),t&&!r.ssr&&((i=this._viewport=document.createElement("div")).style.cssText="position:relative;overflow:hidden",n=this._svgDom=this._oldVNode.elm=cc("svg"),rp(null,this._oldVNode),i.appendChild(n),t.appendChild(i)),this.resize(r.width,r.height)}function hp(){return function(){}}Yn("canvas",Vu),Yn("svg",ap),t.Arc=yl,t.ArcShape=dl,t.BezierCurve=wl,t.BezierCurveShape=_l,t.BoundingRect=Se,t.Circle=Oa,t.CircleShape=Ia,t.CompoundPath=Lh,t.Displayable=Jn,t.Droplet=Tl,t.DropletShape=kl,t.Element=Ln,t.Ellipse=ja,t.EllipseShape=Ya,t.Group=Bn,t.Heart=Al,t.HeartShape=Ml,t.Image=La,t.IncrementalDisplayable=Oh,t.Isogon=Fl,t.IsogonShape=Rl,t.Line=Ka,t.LineShape=Za,t.LinearGradient=us,t.OrientedBoundingRect=fu,t.Path=ra,t.Pattern=hu,t.Point=fe,t.Polygon=es,t.PolygonShape=ts,t.Polyline=os,t.PolylineShape=ns,t.RadialGradient=fs,t.Rect=Wa,t.RectShape=Ha,t.Ring=El,t.RingShape=Hl,t.Rose=Ul,t.RoseShape=Vl,t.Sector=eh,t.SectorShape=th,t.Star=tu,t.StarShape=Jl,t.TSpan=_s,t.Text=Jh,t.Trochoid=au,t.TrochoidShape=ou,t.color=Jr,t.dispose=function(t){t.dispose()},t.disposeAll=function(){for(var t in En)En.hasOwnProperty(t)&&En[t].dispose();En={}},t.getElementSSRData=jn,t.getInstance=function(t){return En[t]},t.init=function(t,e){var r=new Xn(w(),t,e);return En[r.id]=r},t.matrix=pe,t.morph=Ah,t.parseSVG=function(t,e){return(new ks).parse(t,e)},t.path=Ca,t.registerPainter=Yn,t.registerSSRDataGetter=function(t){Wn=t},t.setPlatformAPI=function(t){for(var e in f)t[e]&&(f[e]=t[e])},t.showDebugDirtyRect=function(t,n){n=n||{};var e=t.painter;if(!e.getLayers)throw new Error("Debug dirty rect can only been used on canvas renderer.");if(e.isSingleCanvas())throw new Error("Debug dirty rect can only been used on zrender inited with container.");var o=document.createElement("div");o.style.cssText="\nposition:absolute;\nleft:0;\ntop:0;\nright:0;\nbottom:0;\npointer-events:none;\n",o.className="ec-debug-dirty-rect-container";var a=[],r=t.dom;r.appendChild(o),"static"===getComputedStyle(r).position&&(r.style.position="relative"),t.on("rendered",function(){if(e.getLayers){var i=0;e.eachBuiltinLayer(function(t){if(t.debugGetPaintRects)for(var e=t.debugGetPaintRects(),r=0;r<e.length;r++)e[r].width&&e[r].height&&(a[i]||(a[i]=new yu(n.style),o.appendChild(a[i].dom)),a[i].show(n.autoHideDelay),a[i].update(e[r]),i++)});for(var t=i;t<a.length;t++)a[t].hide()}})},t.util=ft,t.vector=zt,t.version="5.6.1",Object.defineProperty(t,"__esModule",{value:!0})});