// 紧迫度选项
export const URGENCY_OPTIONS = [
  { value: 'high', label: '高', color: '#ef4444', bgColor: '#fef2f2' },
  { value: 'medium', label: '中', color: '#f59e0b', bgColor: '#fffbeb' },
  { value: 'low', label: '低', color: '#10b981', bgColor: '#f0fdf4' }
]

// 重要度选项
export const IMPORTANCE_OPTIONS = [
  { value: 1, label: '1星' },
  { value: 2, label: '2星' },
  { value: 3, label: '3星' },
  { value: 4, label: '4星' },
  { value: 5, label: '5星' }
]

// 排序选项
export const SORT_OPTIONS = [
  { value: 'created_at', label: '创建时间' },
  { value: 'updated_at', label: '更新时间' },
  { value: 'title', label: '标题' },
  { value: 'importance', label: '重要度' },
  { value: 'urgency', label: '紧迫度' }
]

// 排序方向
export const ORDER_OPTIONS = [
  { value: 'desc', label: '降序' },
  { value: 'asc', label: '升序' }
]

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  LIMIT_OPTIONS: [10, 20, 50, 100]
}

// 提醒日期筛选选项
export const REMINDER_FILTER_OPTIONS = [
  { value: 'today', label: '今天' },
  { value: 'week', label: '本周' },
  { value: 'month', label: '本月' },
  { value: 'custom', label: '自定义' }
]

// 默认书签数据
export const DEFAULT_BOOKMARK = {
  title: '',
  url: '',
  tags: [],
  urgency: 'low',
  importance: 1,
  reminder: null,
  comment: ''
}

// 表单验证规则
export const VALIDATION_RULES = {
  title: {
    required: true,
    message: '标题不能为空'
  },
  url: {
    required: true,
    pattern: /^https?:\/\/.+/,
    message: '请输入有效的URL地址'
  }
}

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器错误，请稍后重试',
  VALIDATION_ERROR: '数据验证失败',
  NOT_FOUND: '请求的资源不存在',
  UNKNOWN_ERROR: '未知错误，请联系管理员'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  CREATE_SUCCESS: '书签创建成功',
  UPDATE_SUCCESS: '书签更新成功',
  DELETE_SUCCESS: '书签删除成功',
  BATCH_DELETE_SUCCESS: '批量删除成功',
  BATCH_ADD_TAGS_SUCCESS: '批量添加标签成功',
  EXPORT_SUCCESS: '数据导出成功',
  IMPORT_SUCCESS: '数据导入成功'
}

