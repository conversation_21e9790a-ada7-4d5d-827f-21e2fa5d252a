import axios from 'axios'

// API基础配置
const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:5000/api'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 可以在这里添加认证token等
    console.log('API Request:', config.method.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    console.log('API Response:', response.status, response.config.url)
    return response.data
  },
  error => {
    console.error('Response Error:', error.response?.status, error.response?.data)
    
    // 统一错误处理
    const errorMessage = error.response?.data?.error?.message || error.message || '请求失败'
    
    return Promise.reject({
      status: error.response?.status,
      message: errorMessage,
      code: error.response?.data?.error?.code
    })
  }
)

// API服务类
class BookmarkAPI {
  // 健康检查
  async healthCheck() {
    return await apiClient.get('/health')
  }

  // 获取书签列表
  async getBookmarks(params = {}) {
    const queryParams = new URLSearchParams()
    
    // 处理查询参数
    if (params.search) queryParams.append('search', params.search)
    if (params.tags && params.tags.length > 0) {
      queryParams.append('tags', Array.isArray(params.tags) ? params.tags.join(',') : params.tags)
    }
    if (params.urgency) queryParams.append('urgency', params.urgency)
    if (params.importance) queryParams.append('importance', params.importance)
    if (params.page) queryParams.append('page', params.page)
    if (params.limit) queryParams.append('limit', params.limit)
    if (params.sort) queryParams.append('sort', params.sort)
    if (params.order) queryParams.append('order', params.order)
    
    const queryString = queryParams.toString()
    const url = queryString ? `/bookmarks?${queryString}` : '/bookmarks'
    
    return await apiClient.get(url)
  }

  // 创建书签
  async createBookmark(bookmarkData) {
    return await apiClient.post('/bookmarks', bookmarkData)
  }

  // 获取单个书签
  async getBookmark(id) {
    return await apiClient.get(`/bookmarks/${id}`)
  }

  // 更新书签
  async updateBookmark(id, bookmarkData) {
    return await apiClient.put(`/bookmarks/${id}`, bookmarkData)
  }

  // 删除书签
  async deleteBookmark(id) {
    return await apiClient.delete(`/bookmarks/${id}`)
  }

  // 批量删除书签
  async batchDeleteBookmarks(ids) {
    return await apiClient.delete('/bookmarks/batch', { data: { ids } })
  }

  // 批量添加标签
  async batchAddTags(bookmarkIds, tags) {
    return await apiClient.post('/bookmarks/batch/tags', {
      bookmark_ids: bookmarkIds,
      tags: tags
    })
  }

  // 获取标签统计
  async getTags() {
    return await apiClient.get('/tags')
  }

  // 获取统计信息
  async getStats() {
    return await apiClient.get('/stats')
  }

  // 导出数据
  async exportData() {
    return await apiClient.get('/sync/export')
  }

  // 导入数据
  async importData(data) {
    return await apiClient.post('/sync/import', data)
  }
}

// 创建API实例
const bookmarkAPI = new BookmarkAPI()

export default bookmarkAPI

