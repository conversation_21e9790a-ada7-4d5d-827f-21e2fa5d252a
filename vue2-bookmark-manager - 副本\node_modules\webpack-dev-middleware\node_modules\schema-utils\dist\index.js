"use strict";

/** @typedef {import("./validate").Schema} Schema */
/** @typedef {import("./validate").JSONSchema4} JSONSchema4 */
/** @typedef {import("./validate").JSONSchema6} JSONSchema6 */
/** @typedef {import("./validate").JSONSchema7} JSONSchema7 */
/** @typedef {import("./validate").ExtendedSchema} ExtendedSchema */

const {
  validate,
  ValidationError,
  enableValidation,
  disableValidation,
  needValidate
} = require("./validate");
module.exports = {
  validate,
  ValidationError,
  enableValidation,
  disableValidation,
  needValidate
};