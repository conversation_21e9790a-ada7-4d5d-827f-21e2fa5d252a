import { URGENCY_OPTIONS, ERROR_MESSAGES } from './constants'

/**
 * 格式化日期
 * @param {string|Date} date - 日期
 * @param {string} format - 格式 ('date' | 'datetime' | 'time')
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'date') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  
  switch (format) {
    case 'datetime':
      return `${year}-${month}-${day} ${hours}:${minutes}`
    case 'time':
      return `${hours}:${minutes}`
    case 'date':
    default:
      return `${year}-${month}-${day}`
  }
}

/**
 * 获取紧迫度配置
 * @param {string} urgency - 紧迫度值
 * @returns {object} 紧迫度配置对象
 */
export function getUrgencyConfig(urgency) {
  return URGENCY_OPTIONS.find(option => option.value === urgency) || URGENCY_OPTIONS[2]
}

/**
 * 生成星级显示
 * @param {number} importance - 重要度 (1-5)
 * @returns {string} 星级字符串
 */
export function generateStars(importance) {
  const filledStars = '★'.repeat(importance)
  const emptyStars = '☆'.repeat(5 - importance)
  return filledStars + emptyStars
}

/**
 * 验证URL格式
 * @param {string} url - URL字符串
 * @returns {boolean} 是否为有效URL
 */
export function isValidUrl(url) {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间(ms)
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间(ms)
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay) {
  let lastCall = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(this, args)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @returns {string} 用户友好的错误消息
 */
export function handleApiError(error) {
  if (!error) return ERROR_MESSAGES.UNKNOWN_ERROR
  
  // 网络错误
  if (!error.status) {
    return ERROR_MESSAGES.NETWORK_ERROR
  }
  
  // 根据状态码返回相应错误消息
  switch (error.status) {
    case 400:
      return error.message || ERROR_MESSAGES.VALIDATION_ERROR
    case 404:
      return ERROR_MESSAGES.NOT_FOUND
    case 500:
      return ERROR_MESSAGES.SERVER_ERROR
    default:
      return error.message || ERROR_MESSAGES.UNKNOWN_ERROR
  }
}

/**
 * 生成随机ID
 * @returns {string} 随机ID字符串
 */
export function generateId() {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * 检查是否为空值
 * @param {any} value - 要检查的值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * 获取相对时间描述
 * @param {string|Date} date - 日期
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date) {
  if (!date) return ''
  
  const now = new Date()
  const target = new Date(date)
  const diffMs = now - target
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '昨天'
  if (diffDays < 7) return `${diffDays}天前`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
  if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`
  return `${Math.floor(diffDays / 365)}年前`
}

/**
 * 高亮搜索关键词
 * @param {string} text - 原文本
 * @param {string} keyword - 搜索关键词
 * @returns {string} 高亮后的HTML
 */
export function highlightKeyword(text, keyword) {
  if (!keyword || !text) return text
  
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

/**
 * 截断文本
 * @param {string} text - 原文本
 * @param {number} maxLength - 最大长度
 * @returns {string} 截断后的文本
 */
export function truncateText(text, maxLength = 100) {
  if (!text || text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

