<template>
  <div class="stats-chart bg-white rounded-lg shadow-md p-4">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">统计信息</h3>
    
    <!-- 总体统计 -->
    <div class="grid grid-cols-2 gap-4 mb-6">
      <div class="text-center p-3 bg-blue-50 rounded-lg">
        <div class="text-2xl font-bold text-blue-600">{{ stats.total_bookmarks || 0 }}</div>
        <div class="text-sm text-gray-600">总书签数</div>
      </div>
      <div class="text-center p-3 bg-green-50 rounded-lg">
        <div class="text-2xl font-bold text-green-600">{{ stats.tags_count || 0 }}</div>
        <div class="text-sm text-gray-600">标签数</div>
      </div>
    </div>

    <!-- 标签使用统计饼图 -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-800 mb-2">标签使用分布</h4>
      <div
        ref="tagsChart"
        class="w-full h-64"
        v-show="tagChartData.length > 0"
      ></div>
      <div
        v-show="tagChartData.length === 0"
        class="w-full h-64 flex items-center justify-center text-gray-500 bg-gray-50 rounded"
      >
        暂无标签数据
      </div>
    </div>

    <!-- 紧迫度分布 -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-800 mb-2">紧迫度分布</h4>
      <div class="space-y-2">
        <div
          v-for="(count, urgency) in stats.urgency_stats"
          :key="urgency"
          class="flex items-center justify-between"
        >
          <div class="flex items-center">
            <span
              class="inline-block w-3 h-3 rounded-full mr-2"
              :style="{ backgroundColor: getUrgencyColor(urgency) }"
            ></span>
            <span class="text-sm text-gray-700">{{ getUrgencyLabel(urgency) }}</span>
          </div>
          <span class="text-sm font-medium text-gray-900">{{ count }}</span>
        </div>
      </div>
    </div>

    <!-- 重要度分布 -->
    <div class="mb-4">
      <h4 class="text-md font-medium text-gray-800 mb-2">重要度分布</h4>
      <div
        ref="importanceChart"
        class="w-full h-48"
        v-show="importanceChartData.length > 0"
      ></div>
      <div
        v-show="importanceChartData.length === 0"
        class="w-full h-48 flex items-center justify-center text-gray-500 bg-gray-50 rounded"
      >
        暂无重要度数据
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { URGENCY_OPTIONS } from '../utils/constants'

export default {
  name: 'StatsChart',
  props: {
    stats: {
      type: Object,
      default: () => ({})
    },
    tagStats: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tagsChart: null,
      importanceChart: null
    }
  },
  computed: {
    tagChartData() {
      return this.tagStats.map(tag => ({
        name: tag.name,
        value: tag.count
      }))
    },
    
    importanceChartData() {
      if (!this.stats.importance_stats) return []
      
      return Object.entries(this.stats.importance_stats).map(([level, count]) => ({
        name: `${level}星`,
        value: count
      }))
    }
  },
  watch: {
    tagChartData: {
      handler() {
        this.$nextTick(() => {
          this.renderTagsChart()
        })
      },
      deep: true
    },
    
    importanceChartData: {
      handler() {
        this.$nextTick(() => {
          this.renderImportanceChart()
        })
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts()
    })
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  
  beforeDestroy() {
    // 清理图表实例
    if (this.tagsChart) {
      this.tagsChart.dispose()
    }
    if (this.importanceChart) {
      this.importanceChart.dispose()
    }
    
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize)
  },
  
  methods: {
    initCharts() {
      // 初始化标签饼图
      if (this.$refs.tagsChart) {
        this.tagsChart = echarts.init(this.$refs.tagsChart)
        this.renderTagsChart()
      }
      
      // 初始化重要度柱状图
      if (this.$refs.importanceChart) {
        this.importanceChart = echarts.init(this.$refs.importanceChart)
        this.renderImportanceChart()
      }
    },
    
    renderTagsChart() {
      if (!this.tagsChart || this.tagChartData.length === 0) return
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '标签使用',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.tagChartData,
            itemStyle: {
              borderRadius: 4,
              borderColor: '#fff',
              borderWidth: 2
            }
          }
        ],
        color: [
          '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
          '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
        ]
      }
      
      this.tagsChart.setOption(option)
    },
    
    renderImportanceChart() {
      if (!this.importanceChart || this.importanceChartData.length === 0) return
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.importanceChartData.map(item => item.name),
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '书签数量',
            type: 'bar',
            barWidth: '60%',
            data: this.importanceChartData.map(item => item.value),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#fbbf24' },
                { offset: 1, color: '#f59e0b' }
              ]),
              borderRadius: [4, 4, 0, 0]
            }
          }
        ]
      }
      
      this.importanceChart.setOption(option)
    },
    
    handleResize() {
      if (this.tagsChart) {
        this.tagsChart.resize()
      }
      if (this.importanceChart) {
        this.importanceChart.resize()
      }
    },
    
    getUrgencyColor(urgency) {
      const option = URGENCY_OPTIONS.find(opt => opt.value === urgency)
      return option ? option.color : '#6b7280'
    },
    
    getUrgencyLabel(urgency) {
      const option = URGENCY_OPTIONS.find(opt => opt.value === urgency)
      return option ? option.label : urgency
    }
  }
}
</script>

<style scoped>
.stats-chart {
  min-height: 600px;
}

/* 确保图表容器有正确的尺寸 */
.stats-chart [ref="tagsChart"],
.stats-chart [ref="importanceChart"] {
  width: 100%;
}
</style>

