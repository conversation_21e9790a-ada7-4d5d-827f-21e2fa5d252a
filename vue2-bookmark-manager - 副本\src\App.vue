<template>
  <div id="app" class="min-h-screen bg-gray-100">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- 左侧标题 -->
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900">书签管理器</h1>
            <span class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
              Vue2
            </span>
          </div>

          <!-- 右侧操作按钮 -->
          <div class="flex items-center space-x-4">
            <!-- 同步按钮 -->
            <button
              @click="showSyncDialog = true"
              class="flex items-center px-3 py-2 text-sm text-gray-700 hover:text-gray-900 transition-colors"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              同步数据
            </button>

            <!-- 导出按钮 -->
            <button
              @click="exportData"
              class="flex items-center px-3 py-2 text-sm text-gray-700 hover:text-gray-900 transition-colors"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              导出
            </button>

            <!-- 健康检查状态 -->
            <div class="flex items-center">
              <div
                class="w-2 h-2 rounded-full mr-2"
                :class="apiHealthy ? 'bg-green-500' : 'bg-red-500'"
              ></div>
              <span class="text-sm text-gray-600">
                {{ apiHealthy ? '已连接' : '连接失败' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-12 gap-6">
        <!-- 左侧筛选栏 -->
        <div class="col-span-3">
          <FilterSidebar
            :filters="filters"
            :available-tags="availableTags"
            @update-filters="handleFiltersUpdate"
          />
        </div>

        <!-- 中间内容区 -->
        <div class="col-span-6">
          <!-- 操作栏 -->
          <div class="bg-white rounded-lg shadow-md p-4 mb-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <button
                  @click="showBookmarkForm = true"
                  class="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  新增书签
                </button>

                <button
                  v-if="selectedBookmarks.length > 0"
                  @click="showBatchTagDialog = true"
                  class="flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a2 2 0 012-2z"></path>
                  </svg>
                  批量添加标签 ({{ selectedBookmarks.length }})
                </button>

                <button
                  v-if="selectedBookmarks.length > 0"
                  @click="batchDeleteBookmarks"
                  class="flex items-center px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                  批量删除 ({{ selectedBookmarks.length }})
                </button>
              </div>

              <div class="text-sm text-gray-600">
                共 {{ pagination.total }} 个书签
              </div>
            </div>
          </div>

          <!-- 书签列表 -->
          <div class="space-y-4">
            <BookmarkCard
              v-for="bookmark in bookmarks"
              :key="bookmark.id"
              :bookmark="bookmark"
              :selected="selectedBookmarks.includes(bookmark.id)"
              :search-keyword="filters.search"
              @select="selectBookmark"
              @toggle-select="toggleSelectBookmark"
            />
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">加载中...</p>
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && bookmarks.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无书签</h3>
            <p class="mt-1 text-sm text-gray-500">开始添加您的第一个书签吧</p>
            <div class="mt-6">
              <button
                @click="showBookmarkForm = true"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                新增书签
              </button>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="pagination.pages > 1" class="mt-6 flex justify-center">
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
              <button
                @click="changePage(pagination.page - 1)"
                :disabled="pagination.page <= 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                上一页
              </button>
              
              <button
                v-for="page in visiblePages"
                :key="page"
                @click="changePage(page)"
                :class="[
                  'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                  page === pagination.page
                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                ]"
              >
                {{ page }}
              </button>
              
              <button
                @click="changePage(pagination.page + 1)"
                :disabled="pagination.page >= pagination.pages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                下一页
              </button>
            </nav>
          </div>
        </div>

        <!-- 右侧详情/统计栏 -->
        <div class="col-span-3">
          <!-- 书签详情表单 -->
          <div v-if="showBookmarkForm || selectedBookmark">
            <BookmarkForm
              :bookmark="selectedBookmark"
              :loading="formLoading"
              @submit="handleBookmarkSubmit"
              @delete="handleBookmarkDelete"
              @close="closeBookmarkForm"
            />
          </div>

          <!-- 统计图表 -->
          <div v-else>
            <StatsChart
              :stats="stats"
              :tag-stats="availableTags"
            />
          </div>
        </div>
      </div>
    </main>

    <!-- 同步对话框 -->
    <div v-if="showSyncDialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">数据同步</h3>
          <div class="space-y-3">
            <button
              @click="syncToCloud"
              class="w-full flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
              </svg>
              上传到云端
            </button>
            <button
              @click="syncFromCloud"
              class="w-full flex items-center justify-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
              从云端下载
            </button>
          </div>
          <div class="mt-4 flex justify-end">
            <button
              @click="showSyncDialog = false"
              class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量添加标签对话框 -->
    <div v-if="showBatchTagDialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">批量添加标签</h3>
          <p class="text-sm text-gray-600 mb-4">为选中的 {{ selectedBookmarks.length }} 个书签添加标签</p>
          
          <div class="mb-4">
            <input
              v-model="batchTagInput"
              type="text"
              placeholder="输入标签，用逗号分隔"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              @keyup.enter="handleBatchAddTags"
            />
          </div>
          
          <div class="flex justify-end space-x-2">
            <button
              @click="showBatchTagDialog = false"
              class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
            >
              取消
            </button>
            <button
              @click="handleBatchAddTags"
              class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              添加
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div
      v-if="message.show"
      class="fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg
              v-if="message.type === 'success'"
              class="h-6 w-6 text-green-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <svg
              v-else
              class="h-6 w-6 text-red-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium text-gray-900">{{ message.text }}</p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="hideMessage"
              class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500"
            >
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BookmarkCard from './components/BookmarkCard.vue'
import BookmarkForm from './components/BookmarkForm.vue'
import FilterSidebar from './components/FilterSidebar.vue'
import StatsChart from './components/StatsChart.vue'
import bookmarkAPI from './services/api'
import { PAGINATION, SUCCESS_MESSAGES } from './utils/constants'
import { handleApiError } from './utils/helpers'

export default {
  name: 'App',
  components: {
    BookmarkCard,
    BookmarkForm,
    FilterSidebar,
    StatsChart
  },
  data() {
    return {
      // 数据状态
      bookmarks: [],
      availableTags: [],
      stats: {},
      
      // UI状态
      loading: false,
      formLoading: false,
      apiHealthy: false,
      
      // 筛选和分页
      filters: {
        search: '',
        tags: [],
        urgency: [],
        importance: [],
        reminderFilter: '',
        reminderStart: '',
        reminderEnd: '',
        sort: 'created_at',
        order: 'desc'
      },
      pagination: {
        page: PAGINATION.DEFAULT_PAGE,
        limit: PAGINATION.DEFAULT_LIMIT,
        total: 0,
        pages: 0
      },
      
      // 选择状态
      selectedBookmarks: [],
      selectedBookmark: null,
      
      // 对话框状态
      showBookmarkForm: false,
      showSyncDialog: false,
      showBatchTagDialog: false,
      
      // 表单数据
      batchTagInput: '',
      
      // 消息提示
      message: {
        show: false,
        type: 'success',
        text: ''
      }
    }
  },
  computed: {
    visiblePages() {
      const current = this.pagination.page
      const total = this.pagination.pages
      const delta = 2
      
      const range = []
      const rangeWithDots = []
      
      for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
        range.push(i)
      }
      
      if (current - delta > 2) {
        rangeWithDots.push(1, '...')
      } else {
        rangeWithDots.push(1)
      }
      
      rangeWithDots.push(...range)
      
      if (current + delta < total - 1) {
        rangeWithDots.push('...', total)
      } else {
        rangeWithDots.push(total)
      }
      
      return rangeWithDots.filter((item, index, arr) => arr.indexOf(item) === index && item !== '...')
    }
  },
  async created() {
    await this.checkApiHealth()
    await this.loadInitialData()
  },
  methods: {
    // API健康检查
    async checkApiHealth() {
      try {
        await bookmarkAPI.healthCheck()
        this.apiHealthy = true
      } catch (error) {
        this.apiHealthy = false
        console.error('API健康检查失败:', error)
      }
    },
    
    // 加载初始数据
    async loadInitialData() {
      await Promise.all([
        this.loadBookmarks(),
        this.loadTags(),
        this.loadStats()
      ])
    },
    
    // 加载书签列表
    async loadBookmarks() {
      this.loading = true
      try {
        const params = {
          ...this.filters,
          page: this.pagination.page,
          limit: this.pagination.limit
        }
        
        const response = await bookmarkAPI.getBookmarks(params)
        
        if (response.success) {
          this.bookmarks = response.data.bookmarks
          this.pagination = {
            ...this.pagination,
            total: response.data.total,
            pages: response.data.pages
          }
        }
      } catch (error) {
        this.showMessage('error', handleApiError(error))
      } finally {
        this.loading = false
      }
    },
    
    // 加载标签统计
    async loadTags() {
      try {
        const response = await bookmarkAPI.getTags()
        if (response.success) {
          this.availableTags = response.data
        }
      } catch (error) {
        console.error('加载标签失败:', error)
      }
    },
    
    // 加载统计信息
    async loadStats() {
      try {
        const response = await bookmarkAPI.getStats()
        if (response.success) {
          this.stats = response.data
        }
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    },
    
    // 处理筛选更新
    async handleFiltersUpdate(newFilters) {
      this.filters = { ...newFilters }
      this.pagination.page = 1
      this.selectedBookmarks = []
      await this.loadBookmarks()
    },
    
    // 分页处理
    async changePage(page) {
      if (page >= 1 && page <= this.pagination.pages) {
        this.pagination.page = page
        await this.loadBookmarks()
      }
    },
    
    // 选择书签
    selectBookmark(bookmark) {
      this.selectedBookmark = bookmark
      this.showBookmarkForm = false
    },
    
    // 切换书签选择状态
    toggleSelectBookmark(bookmark) {
      const index = this.selectedBookmarks.indexOf(bookmark.id)
      if (index > -1) {
        this.selectedBookmarks.splice(index, 1)
      } else {
        this.selectedBookmarks.push(bookmark.id)
      }
    },
    
    // 处理书签表单提交
    async handleBookmarkSubmit(bookmarkData) {
      this.formLoading = true
      try {
        let response
        
        if (this.selectedBookmark) {
          // 更新书签
          response = await bookmarkAPI.updateBookmark(this.selectedBookmark.id, bookmarkData)
        } else {
          // 创建书签
          response = await bookmarkAPI.createBookmark(bookmarkData)
        }
        
        if (response.success) {
          this.showMessage('success', this.selectedBookmark ? SUCCESS_MESSAGES.UPDATE_SUCCESS : SUCCESS_MESSAGES.CREATE_SUCCESS)
          this.closeBookmarkForm()
          await this.loadInitialData()
        }
      } catch (error) {
        this.showMessage('error', handleApiError(error))
      } finally {
        this.formLoading = false
      }
    },
    
    // 处理书签删除
    async handleBookmarkDelete(bookmarkId) {
      this.formLoading = true
      try {
        const response = await bookmarkAPI.deleteBookmark(bookmarkId)
        
        if (response.success) {
          this.showMessage('success', SUCCESS_MESSAGES.DELETE_SUCCESS)
          this.closeBookmarkForm()
          await this.loadInitialData()
        }
      } catch (error) {
        this.showMessage('error', handleApiError(error))
      } finally {
        this.formLoading = false
      }
    },
    
    // 批量删除书签
    async batchDeleteBookmarks() {
      if (!confirm(`确定要删除选中的 ${this.selectedBookmarks.length} 个书签吗？`)) {
        return
      }
      
      try {
        const response = await bookmarkAPI.batchDeleteBookmarks(this.selectedBookmarks)
        
        if (response.success) {
          this.showMessage('success', SUCCESS_MESSAGES.BATCH_DELETE_SUCCESS)
          this.selectedBookmarks = []
          await this.loadInitialData()
        }
      } catch (error) {
        this.showMessage('error', handleApiError(error))
      }
    },
    
    // 批量添加标签
    async handleBatchAddTags() {
      if (!this.batchTagInput.trim()) {
        return
      }
      
      const tags = this.batchTagInput.split(',').map(tag => tag.trim()).filter(tag => tag)
      
      try {
        const response = await bookmarkAPI.batchAddTags(this.selectedBookmarks, tags)
        
        if (response.success) {
          this.showMessage('success', SUCCESS_MESSAGES.BATCH_ADD_TAGS_SUCCESS)
          this.showBatchTagDialog = false
          this.batchTagInput = ''
          this.selectedBookmarks = []
          await this.loadInitialData()
        }
      } catch (error) {
        this.showMessage('error', handleApiError(error))
      }
    },
    
    // 数据同步
    async syncToCloud() {
      try {
        const response = await bookmarkAPI.exportData()
        if (response.success) {
          this.showMessage('success', '数据已上传到云端')
          this.showSyncDialog = false
        }
      } catch (error) {
        this.showMessage('error', handleApiError(error))
      }
    },
    
    async syncFromCloud() {
      try {
        const response = await bookmarkAPI.exportData()
        if (response.success) {
          await bookmarkAPI.importData(response.data)
          this.showMessage('success', '数据已从云端下载')
          this.showSyncDialog = false
          await this.loadInitialData()
        }
      } catch (error) {
        this.showMessage('error', handleApiError(error))
      }
    },
    
    // 导出数据
    async exportData() {
      try {
        const response = await bookmarkAPI.exportData()
        if (response.success) {
          const dataStr = JSON.stringify(response.data, null, 2)
          const dataBlob = new Blob([dataStr], { type: 'application/json' })
          const url = URL.createObjectURL(dataBlob)
          const link = document.createElement('a')
          link.href = url
          link.download = `bookmarks-${new Date().toISOString().split('T')[0]}.json`
          link.click()
          URL.revokeObjectURL(url)
          
          this.showMessage('success', SUCCESS_MESSAGES.EXPORT_SUCCESS)
        }
      } catch (error) {
        this.showMessage('error', handleApiError(error))
      }
    },
    
    // 关闭书签表单
    closeBookmarkForm() {
      this.showBookmarkForm = false
      this.selectedBookmark = null
    },
    
    // 显示消息
    showMessage(type, text) {
      this.message = {
        show: true,
        type,
        text
      }
      
      // 3秒后自动隐藏
      setTimeout(() => {
        this.hideMessage()
      }, 3000)
    },
    
    // 隐藏消息
    hideMessage() {
      this.message.show = false
    }
  }
}
</script>

<style>
/* 全局样式 */
#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .grid-cols-12 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .col-span-3,
  .col-span-6 {
    grid-column: span 1 / span 1;
  }
}
</style>

