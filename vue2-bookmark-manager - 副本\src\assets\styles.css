/* 基础样式 - 替代 Tailwind CSS */

/* 自定义样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
}

/* 自定义组件样式 */
.bookmark-card {
  transition: all 0.2s ease-in-out;
}

.bookmark-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 表单样式增强 */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

/* 按钮样式增强 */
.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-outline {
  @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-blue-500;
}

/* 卡片样式 */
.card {
  @apply bg-white overflow-hidden shadow rounded-lg;
}

.card-header {
  @apply px-4 py-5 sm:px-6 border-b border-gray-200;
}

.card-body {
  @apply px-4 py-5 sm:p-6;
}

.card-footer {
  @apply px-4 py-4 sm:px-6 bg-gray-50 border-t border-gray-200;
}

/* 标签样式 */
.tag {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.tag-blue {
  @apply bg-blue-100 text-blue-800;
}

.tag-green {
  @apply bg-green-100 text-green-800;
}

.tag-yellow {
  @apply bg-yellow-100 text-yellow-800;
}

.tag-red {
  @apply bg-red-100 text-red-800;
}

.tag-gray {
  @apply bg-gray-100 text-gray-800;
}

/* 加载动画 */
.loading-spinner {
  @apply inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite];
}

/* 紧迫度颜色 */
.urgency-high {
  @apply text-red-600 bg-red-50 border-red-200;
}

.urgency-medium {
  @apply text-yellow-600 bg-yellow-50 border-yellow-200;
}

.urgency-low {
  @apply text-green-600 bg-green-50 border-green-200;
}

/* 重要度星级 */
.star-rating {
  @apply flex items-center;
}

.star {
  @apply text-yellow-400 cursor-pointer transition-colors duration-150;
}

.star:hover,
.star.active {
  @apply text-yellow-500;
}

.star.inactive {
  @apply text-gray-300;
}

/* 搜索高亮 */
mark {
  @apply bg-yellow-200 text-yellow-900 px-1 py-0.5 rounded;
}

/* 响应式网格 */
.responsive-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* 滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter,
.slide-leave-to {
  transform: translateX(100%);
}

/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.modal-container {
  @apply relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white;
}

/* 消息提示样式 */
.message-success {
  @apply bg-green-50 border border-green-200 text-green-800;
}

.message-error {
  @apply bg-red-50 border border-red-200 text-red-800;
}

.message-warning {
  @apply bg-yellow-50 border border-yellow-200 text-yellow-800;
}

.message-info {
  @apply bg-blue-50 border border-blue-200 text-blue-800;
}

/* 空状态样式 */
.empty-state {
  @apply text-center py-12;
}

.empty-state-icon {
  @apply mx-auto h-12 w-12 text-gray-400;
}

.empty-state-title {
  @apply mt-2 text-sm font-medium text-gray-900;
}

.empty-state-description {
  @apply mt-1 text-sm text-gray-500;
}

/* 分页样式 */
.pagination {
  @apply flex items-center justify-center space-x-1;
}

.pagination-item {
  @apply relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md transition-colors duration-200;
}

.pagination-item.active {
  @apply z-10 bg-blue-50 border-blue-500 text-blue-600;
}

.pagination-item:not(.active) {
  @apply bg-white border-gray-300 text-gray-500 hover:bg-gray-50;
}

.pagination-item:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 工具提示样式 */
.tooltip {
  @apply absolute z-10 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg;
}

/* 下拉菜单样式 */
.dropdown {
  @apply relative inline-block text-left;
}

.dropdown-menu {
  @apply absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none;
}

.dropdown-item {
  @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900;
}

/* 表格样式 */
.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* 进度条样式 */
.progress {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-bar {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

/* 开关样式 */
.switch {
  @apply relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.switch.enabled {
  @apply bg-blue-600;
}

.switch.disabled {
  @apply bg-gray-200;
}

.switch-thumb {
  @apply inline-block h-4 w-4 transform rounded-full bg-white transition-transform;
}

.switch.enabled .switch-thumb {
  @apply translate-x-6;
}

.switch.disabled .switch-thumb {
  @apply translate-x-1;
}

/* 媒体查询 */
@media (max-width: 640px) {
  .responsive-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-container {
    @apply w-full mx-4;
  }
}

@media (max-width: 768px) {
  .card-body {
    @apply px-4 py-4;
  }
  
  .btn {
    @apply px-3 py-2 text-xs;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-after: always;
  }
}

